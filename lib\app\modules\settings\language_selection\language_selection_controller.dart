import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/storage_service.dart';

class LanguageSelectionController extends GetxController {
  final RxString selectedLanguage = 'English'.obs;
  final RxString selectedRegion = 'United States'.obs;
  final RxBool autoDetectLanguage = false.obs;

  final Map<String, Map<String, dynamic>> availableLanguages = {
    'English': {
      'name': 'English',
      'nativeName': 'English',
      'code': 'en',
      'flag': '🇺🇸',
      'regions': ['United States', 'United Kingdom', 'Canada', 'Australia'],
    },
    'Spanish': {
      'name': 'Spanish',
      'nativeName': 'Español',
      'code': 'es',
      'flag': '🇪🇸',
      'regions': ['Spain', 'Mexico', 'Argentina', 'Colombia'],
    },
    'French': {
      'name': 'French',
      'nativeName': 'Français',
      'code': 'fr',
      'flag': '🇫🇷',
      'regions': ['France', 'Canada', 'Belgium', 'Switzerland'],
    },
    'German': {
      'name': 'German',
      'nativeName': 'Deutsch',
      'code': 'de',
      'flag': '🇩🇪',
      'regions': ['Germany', 'Austria', 'Switzerland'],
    },
    'Chinese': {
      'name': 'Chinese',
      'nativeName': '中文',
      'code': 'zh',
      'flag': '🇨🇳',
      'regions': ['China', 'Taiwan', 'Hong Kong', 'Singapore'],
    },
    'Japanese': {
      'name': 'Japanese',
      'nativeName': '日本語',
      'code': 'ja',
      'flag': '🇯🇵',
      'regions': ['Japan'],
    },
    'Korean': {
      'name': 'Korean',
      'nativeName': '한국어',
      'code': 'ko',
      'flag': '🇰🇷',
      'regions': ['South Korea'],
    },
    'Portuguese': {
      'name': 'Portuguese',
      'nativeName': 'Português',
      'code': 'pt',
      'flag': '🇵🇹',
      'regions': ['Portugal', 'Brazil'],
    },
    'Italian': {
      'name': 'Italian',
      'nativeName': 'Italiano',
      'code': 'it',
      'flag': '🇮🇹',
      'regions': ['Italy'],
    },
    'Russian': {
      'name': 'Russian',
      'nativeName': 'Русский',
      'code': 'ru',
      'flag': '🇷🇺',
      'regions': ['Russia'],
    },
    'Arabic': {
      'name': 'Arabic',
      'nativeName': 'العربية',
      'code': 'ar',
      'flag': '🇸🇦',
      'regions': ['Saudi Arabia', 'UAE', 'Egypt', 'Morocco'],
    },
    'Hindi': {
      'name': 'Hindi',
      'nativeName': 'हिन्दी',
      'code': 'hi',
      'flag': '🇮🇳',
      'regions': ['India'],
    },
  };

  @override
  void onInit() {
    super.onInit();
    _loadLanguageSettings();
  }

  void _loadLanguageSettings() {
    final storage = StorageService.to;
    selectedLanguage.value = storage.getString('selected_language').isEmpty
        ? 'English'
        : storage.getString('selected_language');
    selectedRegion.value = storage.getString('selected_region').isEmpty
        ? 'United States'
        : storage.getString('selected_region');
    autoDetectLanguage.value = storage.getBool('auto_detect_language');
  }

  void changeLanguage(String language) {
    selectedLanguage.value = language;

    // Set default region for the language
    final regions = availableLanguages[language]?['regions'] as List<String>?;
    if (regions != null && regions.isNotEmpty) {
      selectedRegion.value = regions.first;
      StorageService.to.setString('selected_region', selectedRegion.value);
    }

    StorageService.to.setString('selected_language', language);

    // Here you would typically update the app's locale
    // For now, we'll just show a snackbar
    Get.snackbar(
      'Language Changed',
      'Language changed to ${availableLanguages[language]?['nativeName']}',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }

  void changeRegion(String region) {
    selectedRegion.value = region;
    StorageService.to.setString('selected_region', region);

    Get.snackbar(
      'Region Changed',
      'Region changed to $region',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void toggleAutoDetectLanguage() {
    autoDetectLanguage.value = !autoDetectLanguage.value;
    StorageService.to.setBool('auto_detect_language', autoDetectLanguage.value);

    if (autoDetectLanguage.value) {
      Get.snackbar(
        'Auto-Detect Language',
        'Language will be detected automatically based on your device settings',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    } else {
      Get.snackbar(
        'Auto-Detect Language',
        'Manual language selection enabled',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    }
  }

  List<String> getAvailableRegions() {
    final regions =
        availableLanguages[selectedLanguage.value]?['regions'] as List<String>?;
    return regions ?? [];
  }

  String getLanguageFlag(String language) {
    return availableLanguages[language]?['flag'] ?? '🌐';
  }

  String getLanguageNativeName(String language) {
    return availableLanguages[language]?['nativeName'] ?? language;
  }

  String getLanguageCode(String language) {
    return availableLanguages[language]?['code'] ?? 'en';
  }

  void showRegionDialog() {
    final regions = getAvailableRegions();
    if (regions.isEmpty) return;

    Get.dialog(
      AlertDialog(
        title: Text('Select Region for ${selectedLanguage.value}'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: regions.length,
            itemBuilder: (context, index) {
              final region = regions[index];
              return Obx(() => RadioListTile<String>(
                    title: Text(region),
                    value: region,
                    groupValue: selectedRegion.value,
                    onChanged: (value) {
                      if (value != null) {
                        changeRegion(value);
                        Get.back();
                      }
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                  ));
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void downloadLanguagePack(String language) {
    Get.dialog(
      AlertDialog(
        title: const Text('Download Language Pack'),
        content: Text(
            'Download ${availableLanguages[language]?['nativeName']} language pack for offline use?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _simulateDownload(language);
            },
            child: const Text('Download'),
          ),
        ],
      ),
    );
  }

  void _simulateDownload(String language) {
    Get.snackbar(
      'Downloading',
      'Downloading ${availableLanguages[language]?['nativeName']} language pack...',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      showProgressIndicator: true,
    );

    // Simulate download completion
    Future.delayed(const Duration(seconds: 3), () {
      Get.snackbar(
        'Download Complete',
        '${availableLanguages[language]?['nativeName']} language pack downloaded successfully',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    });
  }
}
