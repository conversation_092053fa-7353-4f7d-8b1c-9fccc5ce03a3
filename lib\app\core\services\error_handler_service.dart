import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'navigation_service.dart';

/// Centralized error handling service for the entire application
class ErrorHandlerService extends GetxService {
  static ErrorHandlerService get to => Get.find();

  /// Handle and display errors with appropriate user feedback
  void handleError(
    dynamic error, {
    String? context,
    bool showSnackbar = true,
    bool logError = true,
    VoidCallback? onRetry,
  }) {
    final errorInfo = _parseError(error);

    if (logError) {
      _logError(error, context, errorInfo);
    }

    if (showSnackbar) {
      _showErrorSnackbar(errorInfo, onRetry);
    }
  }

  /// Parse error and return user-friendly message and type
  ErrorInfo _parseError(dynamic error) {
    if (error is DioException) {
      return _parseDioError(error);
    } else if (error is String) {
      return ErrorInfo(
        message: error,
        type: ErrorType.general,
        isRetryable: false,
      );
    } else if (error is Exception) {
      return ErrorInfo(
        message: error.toString(),
        type: ErrorType.general,
        isRetryable: false,
      );
    } else {
      return ErrorInfo(
        message: 'An unexpected error occurred',
        type: ErrorType.unknown,
        isRetryable: true,
      );
    }
  }

  /// Parse Dio errors specifically
  ErrorInfo _parseDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ErrorInfo(
          message: 'Connection timeout. Please check your internet connection.',
          type: ErrorType.network,
          isRetryable: true,
        );

      case DioExceptionType.connectionError:
        return ErrorInfo(
          message: 'No internet connection. Please check your network.',
          type: ErrorType.network,
          isRetryable: true,
        );

      case DioExceptionType.badResponse:
        return _parseHttpError(error);

      case DioExceptionType.cancel:
        return ErrorInfo(
          message: 'Request was cancelled',
          type: ErrorType.cancelled,
          isRetryable: false,
        );

      default:
        return ErrorInfo(
          message: 'Network error occurred. Please try again.',
          type: ErrorType.network,
          isRetryable: true,
        );
    }
  }

  /// Parse HTTP response errors
  ErrorInfo _parseHttpError(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;
    String message = 'Server error occurred';
    ErrorType type = ErrorType.server;
    bool isRetryable = false;

    switch (statusCode) {
      case 400:
        message = responseData?['message'] ?? 'Invalid request';
        type = ErrorType.validation;
        break;
      case 401:
        message = 'Authentication failed. Please login again.';
        type = ErrorType.authentication;
        break;
      case 403:
        message = 'Access denied. You don\'t have permission for this action.';
        type = ErrorType.authorization;
        break;
      case 404:
        message = 'Resource not found';
        type = ErrorType.notFound;
        break;
      case 422:
        message = responseData?['message'] ?? 'Validation error';
        type = ErrorType.validation;
        break;
      case 429:
        message = 'Too many requests. Please try again later.';
        type = ErrorType.rateLimit;
        isRetryable = true;
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        message = 'Server error. Please try again later.';
        type = ErrorType.server;
        isRetryable = true;
        break;
      default:
        message = responseData?['message'] ?? 'An error occurred';
        type = ErrorType.server;
        isRetryable = true;
    }

    return ErrorInfo(
      message: message,
      type: type,
      isRetryable: isRetryable,
      statusCode: statusCode,
    );
  }

  /// Log error for debugging and analytics
  void _logError(dynamic error, String? context, ErrorInfo errorInfo) {
    if (kDebugMode) {
      print('🚨 Error in ${context ?? 'Unknown'}: ${error.toString()}');
      print('📝 User message: ${errorInfo.message}');
      print('🏷️ Error type: ${errorInfo.type}');
      if (errorInfo.statusCode != null) {
        print('📊 Status code: ${errorInfo.statusCode}');
      }
    }

    // In production, send to analytics/crash reporting service
    // AnalyticsService.to.logError(error, context, errorInfo);
  }

  /// Show error snackbar with appropriate styling
  void _showErrorSnackbar(ErrorInfo errorInfo, VoidCallback? onRetry) {
    final navigationService = NavigationService.to;

    Widget? mainButton;
    if (errorInfo.isRetryable && onRetry != null) {
      mainButton = TextButton(
        onPressed: () {
          Get.closeCurrentSnackbar();
          onRetry();
        },
        child: const Text('Retry', style: TextStyle(color: Colors.white)),
      );
    }

    navigationService.showSnackbar(
      title: _getErrorTitle(errorInfo.type),
      message: errorInfo.message,
      backgroundColor: _getErrorColor(errorInfo.type),
      colorText: Colors.white,
      icon: Icon(_getErrorIcon(errorInfo.type), color: Colors.white),
      duration: Duration(seconds: errorInfo.isRetryable ? 5 : 3),
    );

    // Show retry button as a separate action if needed
    if (mainButton != null) {
      Future.delayed(const Duration(milliseconds: 500), () {
        Get.rawSnackbar(
          message: '',
          mainButton: mainButton,
          backgroundColor: Colors.transparent,
          duration: const Duration(seconds: 4),
        );
      });
    }
  }

  /// Get error title based on type
  String _getErrorTitle(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return 'Connection Error';
      case ErrorType.authentication:
        return 'Authentication Error';
      case ErrorType.authorization:
        return 'Access Denied';
      case ErrorType.validation:
        return 'Validation Error';
      case ErrorType.notFound:
        return 'Not Found';
      case ErrorType.rateLimit:
        return 'Rate Limit';
      case ErrorType.server:
        return 'Server Error';
      case ErrorType.cancelled:
        return 'Cancelled';
      case ErrorType.general:
      case ErrorType.unknown:
        return 'Error';
    }
  }

  /// Get error color based on type
  Color _getErrorColor(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return Colors.orange;
      case ErrorType.authentication:
      case ErrorType.authorization:
        return Colors.red;
      case ErrorType.validation:
        return Colors.amber;
      case ErrorType.notFound:
        return Colors.grey;
      case ErrorType.rateLimit:
        return Colors.purple;
      case ErrorType.server:
        return Colors.red;
      case ErrorType.cancelled:
        return Colors.blue;
      case ErrorType.general:
      case ErrorType.unknown:
        return Colors.red;
    }
  }

  /// Get error icon based on type
  IconData _getErrorIcon(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.authentication:
        return Icons.lock;
      case ErrorType.authorization:
        return Icons.block;
      case ErrorType.validation:
        return Icons.warning;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.rateLimit:
        return Icons.timer;
      case ErrorType.server:
        return Icons.error;
      case ErrorType.cancelled:
        return Icons.cancel;
      case ErrorType.general:
      case ErrorType.unknown:
        return Icons.error;
    }
  }

  /// Show success message
  void showSuccess(String message) {
    NavigationService.to.showSuccess(message);
  }

  /// Show info message
  void showInfo(String message) {
    NavigationService.to.showInfo(message);
  }

  /// Show warning message
  void showWarning(String message) {
    NavigationService.to.showWarning(message);
  }
}

/// Error information class
class ErrorInfo {
  final String message;
  final ErrorType type;
  final bool isRetryable;
  final int? statusCode;

  ErrorInfo({
    required this.message,
    required this.type,
    required this.isRetryable,
    this.statusCode,
  });
}

/// Error types for categorization
enum ErrorType {
  network,
  authentication,
  authorization,
  validation,
  notFound,
  rateLimit,
  server,
  cancelled,
  general,
  unknown,
}
