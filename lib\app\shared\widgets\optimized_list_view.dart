import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/services/performance_service.dart';
import '../../core/services/memory_management_service.dart';

/// Performance-optimized list view with lazy loading and memory management
class OptimizedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Future<List<T>> Function(int page)? onLoadMore;
  final Future<void> Function()? onRefresh;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final Widget? errorWidget;
  final ScrollController? scrollController;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final int itemsPerPage;
  final double loadMoreThreshold;
  final bool enableCaching;
  final String? cacheKey;

  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.onLoadMore,
    this.onRefresh,
    this.loadingWidget,
    this.emptyWidget,
    this.errorWidget,
    this.scrollController,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.itemsPerPage = 20,
    this.loadMoreThreshold = 200.0,
    this.enableCaching = true,
    this.cacheKey,
  });

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>>
    with AutomaticKeepAliveClientMixin {
  late ScrollController _scrollController;
  final RxBool _isLoadingMore = false.obs;
  final RxBool _hasError = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxBool _hasMoreData = true.obs;

  // Performance tracking
  late String _performanceKey;

  // Memory management
  final Map<int, Widget> _cachedWidgets = {};
  static const int maxCachedWidgets = 50;

  @override
  bool get wantKeepAlive => widget.enableCaching;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _performanceKey = 'list_${widget.cacheKey ?? widget.runtimeType}';

    _scrollController.addListener(_onScroll);

    // Monitor memory usage
    if (MemoryManagementService.to.isLowMemoryMode) {
      _enableLowMemoryMode();
    }
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    _cachedWidgets.clear();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - widget.loadMoreThreshold) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore.value ||
        !_hasMoreData.value ||
        widget.onLoadMore == null) {
      return;
    }

    final timer =
        PerformanceService.to.startTimer('${_performanceKey}_load_more');

    try {
      _isLoadingMore.value = true;
      _hasError.value = false;

      final currentPage = (widget.items.length / widget.itemsPerPage).ceil();
      final newItems = await widget.onLoadMore!(currentPage + 1);

      if (newItems.isEmpty) {
        _hasMoreData.value = false;
      } else {
        // Add new items to the list
        widget.items.addAll(newItems);
        setState(() {});
      }
    } catch (e) {
      _hasError.value = true;
      _errorMessage.value = e.toString();
    } finally {
      _isLoadingMore.value = false;
      timer.stop();
    }
  }

  Future<void> _onRefresh() async {
    final timer =
        PerformanceService.to.startTimer('${_performanceKey}_refresh');

    try {
      _hasError.value = false;
      _hasMoreData.value = true;
      _cachedWidgets.clear();

      if (widget.onRefresh != null) {
        await widget.onRefresh!();
      }

      setState(() {});
    } finally {
      timer.stop();
    }
  }

  Widget _buildCachedItem(BuildContext context, int index) {
    // Use cached widget if available and not in low memory mode
    if (widget.enableCaching &&
        !MemoryManagementService.to.isLowMemoryMode &&
        _cachedWidgets.containsKey(index)) {
      return _cachedWidgets[index]!;
    }

    final item = widget.items[index];
    final builtWidget = widget.itemBuilder(context, item, index);

    // Cache the widget if caching is enabled and we're not in low memory mode
    if (widget.enableCaching && !MemoryManagementService.to.isLowMemoryMode) {
      _cacheWidget(index, builtWidget);
    }

    return builtWidget;
  }

  void _cacheWidget(int index, Widget widget) {
    _cachedWidgets[index] = widget;

    // Limit cache size to prevent memory issues
    if (_cachedWidgets.length > maxCachedWidgets) {
      final oldestKey = _cachedWidgets.keys.first;
      _cachedWidgets.remove(oldestKey);
    }
  }

  void _enableLowMemoryMode() {
    // Clear cached widgets in low memory mode
    _cachedWidgets.clear();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (widget.items.isEmpty) {
      return widget.emptyWidget ?? _buildEmptyWidget();
    }

    Widget listView = ListView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      itemCount: widget.items.length + (_hasMoreData.value ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == widget.items.length) {
          // Loading more indicator
          return Obx(() => _buildLoadMoreWidget());
        }

        return _buildCachedItem(context, index);
      },
    );

    if (widget.onRefresh != null) {
      listView = RefreshIndicator(
        onRefresh: _onRefresh,
        child: listView,
      );
    }

    return Obx(() {
      if (_hasError.value) {
        return widget.errorWidget ?? _buildErrorWidget();
      }

      return listView;
    });
  }

  Widget _buildLoadMoreWidget() {
    if (_isLoadingMore.value) {
      return widget.loadingWidget ?? _buildDefaultLoadingWidget();
    } else if (_hasError.value) {
      return _buildLoadMoreErrorWidget();
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildDefaultLoadingWidget() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildLoadMoreErrorWidget() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Text(
            'Failed to load more items',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.red,
                ),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: _loadMore,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No items to display',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading items',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage.value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _onRefresh,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}

/// Optimized grid view with similar performance features
class OptimizedGridView<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;
  final EdgeInsets? padding;
  final ScrollController? scrollController;
  final ScrollPhysics? physics;

  const OptimizedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.childAspectRatio = 1.0,
    this.padding,
    this.scrollController,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: scrollController,
      padding: padding,
      physics: physics,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return itemBuilder(context, items[index], index);
      },
    );
  }
}
