import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../controllers/animation_controller_manager.dart';

/// Floating indicator widget for enhanced bottom navigation
class FloatingIndicator extends StatelessWidget {
  final int currentIndex;
  final int itemCount;
  final Color color;
  final bool enabled;
  final AnimationControllerManager animationManager;

  const FloatingIndicator({
    super.key,
    required this.currentIndex,
    required this.itemCount,
    required this.color,
    required this.enabled,
    required this.animationManager,
  });

  @override
  Widget build(BuildContext context) {
    if (!enabled) return const SizedBox.shrink();

    return LayoutBuilder(
      builder: (context, constraints) {
        return AnimatedBuilder(
          animation: Listenable.merge([
            animationManager.indicatorSlideAnimation,
            animationManager.floatingAnimation,
          ]),
          builder: (context, child) {
            return Positioned(
              left: _calculateIndicatorPosition(constraints.maxWidth),
              top: 8 +
                  (math.sin(animationManager.floatingAnimation.value *
                          2 *
                          math.pi) *
                      2),
              child: Container(
                width: 32,
                height: 4,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  double _calculateIndicatorPosition(double containerWidth) {
    final itemWidth = containerWidth / itemCount;
    final basePosition = currentIndex * itemWidth;
    final slideOffset = animationManager.indicatorSlideAnimation.value * 0.1;

    // Center the indicator within each item
    return basePosition + (itemWidth / 2) - 16 + slideOffset;
  }
}
