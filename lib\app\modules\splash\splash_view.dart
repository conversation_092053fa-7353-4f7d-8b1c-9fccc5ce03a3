import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/animated_background.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import 'splash_controller.dart';

class SplashView extends GetView<SplashController> {
  const SplashView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBackground(
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo Animation
                Obx(() => AnimatedScale(
                      scale: controller.logoScale.value,
                      duration: AppConstants.longAnimationDuration,
                      curve: Curves.elasticOut,
                      child: AnimatedOpacity(
                        opacity: controller.logoOpacity.value,
                        duration: AppConstants.animationDuration,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: Theme.of(context)
                                  .auroraGradients
                                  .primaryAuroraGradient,
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Theme.of(context)
                                    .colorScheme
                                    .primary
                                    .withValues(alpha: 0.3),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.flash_on_rounded,
                            size: 60,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    )),

                const SizedBox(height: 32),

                // App Name Animation
                Obx(() => AnimatedOpacity(
                      opacity: controller.textOpacity.value,
                      duration: AppConstants.longAnimationDuration,
                      child: Column(
                        children: [
                          Text(
                            AppConstants.appName,
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                  letterSpacing: 1.2,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Modern & Beautiful',
                            style:
                                Theme.of(context).textTheme.bodyLarge?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withValues(alpha: 0.7),
                                      letterSpacing: 0.5,
                                    ),
                          ),
                        ],
                      ),
                    )),

                const SizedBox(height: 80),

                // Loading Indicator
                Obx(() => AnimatedOpacity(
                      opacity: controller.textOpacity.value,
                      duration: AppConstants.longAnimationDuration,
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withValues(alpha: 0.2),
                        ),
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                    )),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
