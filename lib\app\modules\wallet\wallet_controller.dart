import 'package:get/get.dart';

class WalletController extends GetxController {
  final RxDouble balance = 1250.75.obs;
  final RxList<Transaction> transactions = <Transaction>[].obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadTransactions();
  }

  void _loadTransactions() {
    // Mock transaction data
    transactions.value = [
      Transaction(
        id: '1',
        title: 'Cafeteria Payment',
        amount: -15.50,
        date: DateTime.now().subtract(const Duration(hours: 2)),
        type: TransactionType.expense,
        category: 'Food',
        icon: 'restaurant',
      ),
      Transaction(
        id: '2',
        title: 'Library Fine Payment',
        amount: -5.00,
        date: DateTime.now().subtract(const Duration(days: 1)),
        type: TransactionType.expense,
        category: 'Library',
        icon: 'book',
      ),
      Transaction(
        id: '3',
        title: 'Allowance Deposit',
        amount: 100.00,
        date: DateTime.now().subtract(const Duration(days: 2)),
        type: TransactionType.income,
        category: 'Allowance',
        icon: 'account_balance_wallet',
      ),
      Transaction(
        id: '4',
        title: 'Sports Equipment',
        amount: -25.00,
        date: DateTime.now().subtract(const Duration(days: 3)),
        type: TransactionType.expense,
        category: 'Sports',
        icon: 'sports',
      ),
      Transaction(
        id: '5',
        title: 'Scholarship Credit',
        amount: 200.00,
        date: DateTime.now().subtract(const Duration(days: 5)),
        type: TransactionType.income,
        category: 'Scholarship',
        icon: 'school',
      ),
    ];
  }

  void addMoney() {
    Get.snackbar(
      'Add Money',
      'Add money feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void sendMoney() {
    Get.snackbar(
      'Send Money',
      'Send money feature will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void viewTransactionDetails(Transaction transaction) {
    Get.snackbar(
      'Transaction Details',
      '${transaction.title}: \$${transaction.amount.abs().toStringAsFixed(2)}',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }
}

class Transaction {
  final String id;
  final String title;
  final double amount;
  final DateTime date;
  final TransactionType type;
  final String category;
  final String icon;

  Transaction({
    required this.id,
    required this.title,
    required this.amount,
    required this.date,
    required this.type,
    required this.category,
    required this.icon,
  });
}

enum TransactionType {
  income,
  expense,
}
