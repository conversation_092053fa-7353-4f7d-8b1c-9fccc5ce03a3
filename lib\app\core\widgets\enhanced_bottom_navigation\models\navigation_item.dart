import 'package:flutter/material.dart';

/// Enhanced bottom navigation item model
class EnhancedBottomNavigationItem {
  final IconData icon;
  final IconData? selectedIcon;
  final String label;
  final Color? backgroundColor;
  final Widget? badge;
  final int? badgeCount;
  final String? tooltip;

  const EnhancedBottomNavigationItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
    this.backgroundColor,
    this.badge,
    this.badgeCount,
    this.tooltip,
  });

  /// Get the active icon for selected state
  IconData get effectiveActiveIcon => selectedIcon ?? icon;
}
