import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../shared/widgets/section_header.dart';
import '../../../../shared/widgets/privacy_setting_item.dart';
import '../privacy_settings_controller.dart';

/// Advertising privacy settings section
class AdvertisingSection extends GetView<PrivacySettingsController> {
  const AdvertisingSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Advertising'),
        const SizedBox(height: 12),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.ads_click,
          iconColor: Colors.purple,
          title: 'Personalized Ads',
          subtitle: 'Show ads based on your interests',
          value: controller.personalizedAds.value,
          onChanged: controller.togglePersonalizedAds,
          activeColor: theme.colorScheme.primary,
        )),
      ],
    );
  }
}
