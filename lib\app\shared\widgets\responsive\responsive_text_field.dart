import 'package:flutter/material.dart';
import '../../../core/utils/responsive_utils.dart';

/// Responsive and accessible text field
class ResponsiveTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final ValueChanged<String>? onChanged;
  final String? semanticLabel;
  final String? semanticHint;

  const ResponsiveTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.errorText,
    this.obscureText = false,
    this.keyboardType,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.onChanged,
    this.semanticLabel,
    this.semanticHint,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final responsivePadding = ResponsiveUtils.responsive(
      context,
      mobile: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      tablet: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      desktop: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
    );

    final responsiveTextStyle = ResponsiveUtils.responsive(
      context,
      mobile: theme.textTheme.bodyMedium,
      tablet: theme.textTheme.bodyLarge,
      desktop: theme.textTheme.titleMedium,
    );

    Widget textField = TextField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      onTap: onTap,
      onChanged: onChanged,
      style: responsiveTextStyle,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        errorText: errorText,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        contentPadding: responsivePadding,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.error,
          ),
        ),
      ),
    );

    // Add accessibility features
    if (semanticLabel != null || semanticHint != null) {
      textField = Semantics(
        label: semanticLabel ?? labelText,
        hint: semanticHint ?? hintText,
        textField: true,
        child: textField,
      );
    }

    return textField;
  }
}
