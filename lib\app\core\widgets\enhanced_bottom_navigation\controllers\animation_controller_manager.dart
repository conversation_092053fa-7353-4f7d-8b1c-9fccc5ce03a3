import 'package:flutter/material.dart';

/// Manages all animation controllers for enhanced bottom navigation
class AnimationControllerManager {
  late final AnimationController indicatorController;
  late final AnimationController iconController;
  late final AnimationController rippleController;
  late final AnimationController floatingController;

  late final Animation<double> indicatorSlideAnimation;
  late final Animation<double> iconBounceAnimation;
  late final Animation<double> rippleAnimation;
  late final Animation<double> floatingAnimation;

  void initialize(TickerProvider vsync) {
    indicatorController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 300),
    );

    iconController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 200),
    );

    rippleController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 400),
    );

    floatingController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 3000),
    );

    indicatorSlideAnimation = CurvedAnimation(
      parent: indicatorController,
      curve: Curves.easeInOutCubic,
    );

    iconBounceAnimation = CurvedAnimation(
      parent: iconController,
      curve: Curves.easeOutBack,
    );

    rippleAnimation = CurvedAnimation(
      parent: rippleController,
      curve: Curves.easeOut,
    );

    floatingAnimation = CurvedAnimation(
      parent: floatingController,
      curve: Curves.easeInOut,
    );

    // Start subtle floating animation
    floatingController.repeat(reverse: true);
  }

  void dispose() {
    indicatorController.dispose();
    iconController.dispose();
    rippleController.dispose();
    floatingController.dispose();
  }

  void animateToIndex(int index) {
    // Reset and animate indicator
    indicatorController.reset();
    indicatorController.forward();

    // Trigger icon bounce animation
    iconController.reset();
    iconController.forward().then((_) {
      iconController.reverse();
    });
  }

  void triggerRipple() {
    rippleController.forward().then((_) {
      rippleController.reset();
    });
  }
}
