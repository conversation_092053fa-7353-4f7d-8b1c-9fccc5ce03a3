import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../shared/widgets/section_header.dart';
import '../../../../shared/widgets/privacy_setting_item.dart';
import '../privacy_settings_controller.dart';

/// App permissions privacy settings section
class PermissionsSection extends GetView<PrivacySettingsController> {
  const PermissionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'App Permissions'),
        const SizedBox(height: 12),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.location_on,
          iconColor: Colors.green,
          title: 'Location Access',
          subtitle: 'Allow access to your location',
          value: controller.locationTracking.value,
          onChanged: controller.toggleLocationTracking,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.contacts,
          iconColor: Colors.teal,
          title: 'Contacts Access',
          subtitle: 'Allow access to your contacts',
          value: controller.contactsAccess.value,
          onChanged: controller.toggleContactsAccess,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.camera_alt,
          iconColor: Colors.indigo,
          title: 'Camera Access',
          subtitle: 'Allow access to your camera',
          value: controller.cameraAccess.value,
          onChanged: controller.toggleCameraAccess,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.mic,
          iconColor: Colors.pink,
          title: 'Microphone Access',
          subtitle: 'Allow access to your microphone',
          value: controller.microphoneAccess.value,
          onChanged: controller.toggleMicrophoneAccess,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.photo_library,
          iconColor: Colors.amber,
          title: 'Photo Library Access',
          subtitle: 'Allow access to your photos',
          value: controller.photoLibraryAccess.value,
          onChanged: controller.togglePhotoLibraryAccess,
          activeColor: theme.colorScheme.primary,
        )),
      ],
    );
  }
}
