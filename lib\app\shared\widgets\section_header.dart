import 'package:flutter/material.dart';

/// Reusable section header widget for consistent styling across settings pages
class SectionHeader extends StatelessWidget {
  final String title;
  final TextStyle? textStyle;
  final EdgeInsets? padding;
  final Widget? trailing;

  const SectionHeader({
    super.key,
    required this.title,
    this.textStyle,
    this.padding,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: textStyle ?? theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          if (trailing != null) trailing!,
        ],
      ),
    );
  }
}
