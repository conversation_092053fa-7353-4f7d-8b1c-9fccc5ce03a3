import 'package:flutter/material.dart';
import 'unified_button.dart';
import '../theme/app_theme.dart';

/// Icon-only button widget that uses the unified button system
class UnifiedIconButton extends StatelessWidget {
  const UnifiedIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.variant = ButtonVariant.filled,
    this.size = ButtonSize.medium,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius,
    this.padding,
    this.margin,
    this.iconSize,
    this.elevation = 0.0,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.gradient,
    this.tooltip,
  });

  /// Filled icon button constructor
  const UnifiedIconButton.filled({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.iconSize,
    this.elevation = 2.0,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.tooltip,
  })  : variant = ButtonVariant.filled,
        borderColor = null,
        borderWidth = 1.0,
        gradient = null;

  /// Outlined icon button constructor
  const UnifiedIconButton.outlined({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius,
    this.padding,
    this.margin,
    this.iconSize,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.tooltip,
  })  : variant = ButtonVariant.outlined,
        backgroundColor = null,
        elevation = 0.0,
        gradient = null;

  /// Text icon button constructor
  const UnifiedIconButton.text({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.iconSize,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.tooltip,
  })  : variant = ButtonVariant.text,
        backgroundColor = null,
        borderColor = null,
        borderWidth = 1.0,
        elevation = 0.0,
        gradient = null;

  /// Gradient icon button constructor
  const UnifiedIconButton.gradient({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.gradient,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.iconSize,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.tooltip,
  })  : variant = ButtonVariant.gradient,
        backgroundColor = null,
        borderColor = null,
        borderWidth = 1.0,
        elevation = 0.0;

  final IconData icon;
  final VoidCallback? onPressed;
  final ButtonVariant variant;
  final ButtonSize size;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final double borderWidth;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? iconSize;
  final double elevation;
  final bool enabled;
  final bool loading;
  final Color? loadingColor;
  final List<Color>? gradient;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = enabled && !loading && onPressed != null;

    // Get size-based dimensions
    final dimensions = _getSizeDimensions();
    final effectiveBorderRadius =
        borderRadius ?? BorderRadius.circular(dimensions.borderRadius);

    // Get variant-based colors
    final colors = _getVariantColors(theme);

    Widget buttonChild = _buildButtonContent(theme, dimensions);

    Widget button;

    if (variant == ButtonVariant.gradient) {
      button = _buildGradientButton(
        theme,
        dimensions,
        effectiveBorderRadius,
        buttonChild,
        isEnabled,
      );
    } else {
      button = Container(
        width: dimensions.size,
        height: dimensions.size,
        margin: margin,
        child: Material(
          color: isEnabled
              ? colors.backgroundColor
              : colors.backgroundColor.withValues(alpha: 0.6),
          elevation:
              variant == ButtonVariant.outlined || variant == ButtonVariant.text
                  ? 0
                  : elevation,
          borderRadius: effectiveBorderRadius,
          child: InkWell(
            onTap: isEnabled ? onPressed : null,
            borderRadius: effectiveBorderRadius,
            child: Container(
              decoration:
                  _buildDecoration(colors, effectiveBorderRadius, isEnabled),
              padding: padding ?? EdgeInsets.all(dimensions.padding),
              child: buttonChild,
            ),
          ),
        ),
      );
    }

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }

  _IconButtonDimensions _getSizeDimensions() {
    switch (size) {
      case ButtonSize.small:
        return const _IconButtonDimensions(
          size: 32,
          padding: 6,
          borderRadius: 8,
          iconScale: 0.8,
        );
      case ButtonSize.medium:
        return const _IconButtonDimensions(
          size: 48,
          padding: 12,
          borderRadius: 12,
          iconScale: 1.0,
        );
      case ButtonSize.large:
        return const _IconButtonDimensions(
          size: 56,
          padding: 16,
          borderRadius: 16,
          iconScale: 1.2,
        );
      case ButtonSize.custom:
        return const _IconButtonDimensions(
          size: 48,
          padding: 12,
          borderRadius: 12,
          iconScale: 1.0,
        );
    }
  }

  _ButtonColors _getVariantColors(ThemeData theme) {
    switch (variant) {
      case ButtonVariant.filled:
        return _ButtonColors(
          backgroundColor: backgroundColor ?? theme.colorScheme.primary,
          foregroundColor: foregroundColor ?? theme.colorScheme.onPrimary,
          borderColor: borderColor ?? Colors.transparent,
        );
      case ButtonVariant.outlined:
        return _ButtonColors(
          backgroundColor: backgroundColor ?? Colors.transparent,
          foregroundColor: foregroundColor ?? theme.colorScheme.primary,
          borderColor: borderColor ?? theme.colorScheme.primary,
        );
      case ButtonVariant.text:
        return _ButtonColors(
          backgroundColor: backgroundColor ?? Colors.transparent,
          foregroundColor: foregroundColor ?? theme.colorScheme.primary,
          borderColor: borderColor ?? Colors.transparent,
        );
      case ButtonVariant.gradient:
        return _ButtonColors(
          backgroundColor: backgroundColor ?? Colors.transparent,
          foregroundColor: foregroundColor ?? Colors.white,
          borderColor: borderColor ?? Colors.transparent,
        );
    }
  }

  Widget _buildButtonContent(
      ThemeData theme, _IconButtonDimensions dimensions) {
    if (loading) {
      return SizedBox(
        width: dimensions.size * 0.4,
        height: dimensions.size * 0.4,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            loadingColor ?? _getVariantColors(theme).foregroundColor,
          ),
        ),
      );
    }

    return Icon(
      icon,
      size: iconSize ?? (dimensions.size * 0.5 * dimensions.iconScale),
      color: _getVariantColors(theme).foregroundColor,
    );
  }

  Widget _buildGradientButton(
    ThemeData theme,
    _IconButtonDimensions dimensions,
    BorderRadius effectiveBorderRadius,
    Widget buttonChild,
    bool isEnabled,
  ) {
    final auroraGradients = theme.auroraGradients;
    final effectiveGradient = gradient ??
        (auroraGradients?.primaryAuroraGradient ??
            [
              theme.colorScheme.primary,
              theme.colorScheme.primaryContainer,
            ]);

    return Container(
      width: dimensions.size,
      height: dimensions.size,
      margin: margin,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: effectiveGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: effectiveBorderRadius,
        boxShadow: [
          BoxShadow(
            color: effectiveGradient.first.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: effectiveBorderRadius,
          child: Container(
            padding: padding ?? EdgeInsets.all(dimensions.padding),
            child: buttonChild,
          ),
        ),
      ),
    );
  }

  BoxDecoration? _buildDecoration(
      _ButtonColors colors, BorderRadius borderRadius, bool isEnabled) {
    if (variant == ButtonVariant.outlined) {
      return BoxDecoration(
        border: Border.all(
          color: isEnabled
              ? colors.borderColor
              : colors.borderColor.withValues(alpha: 0.6),
          width: borderWidth,
        ),
        borderRadius: borderRadius,
      );
    }
    return null;
  }
}

class _IconButtonDimensions {
  final double size;
  final double padding;
  final double borderRadius;
  final double iconScale;

  const _IconButtonDimensions({
    required this.size,
    required this.padding,
    required this.borderRadius,
    required this.iconScale,
  });
}

class _ButtonColors {
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;

  const _ButtonColors({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.borderColor,
  });
}
