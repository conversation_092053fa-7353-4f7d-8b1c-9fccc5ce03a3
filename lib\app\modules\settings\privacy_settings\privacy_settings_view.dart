import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'privacy_settings_controller.dart';
import 'widgets/data_collection_section.dart';
import 'widgets/advertising_section.dart';
import 'widgets/permissions_section.dart';
import 'widgets/data_management_section.dart';

class PrivacySettingsView extends GetView<PrivacySettingsController> {
  const PrivacySettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Privacy Settings',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Data Collection Section
            DataCollectionSection(),
            SizedBox(height: 24),

            // Advertising Section
            AdvertisingSection(),
            SizedBox(height: 24),

            // Permissions Section
            PermissionsSection(),
            SizedBox(height: 24),

            // Data Management Section
            DataManagementSection(),
            SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
