import 'package:flutter/material.dart';
import '../../../core/utils/responsive_utils.dart';

/// Responsive app bar
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final double? elevation;
  final String? semanticLabel;

  const ResponsiveAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.elevation,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final responsiveTitleStyle = ResponsiveUtils.responsive(
      context,
      mobile: theme.textTheme.titleLarge,
      tablet: theme.textTheme.headlineSmall,
      desktop: theme.textTheme.headlineMedium,
    );

    return AppBar(
      title: Text(
        title,
        style: responsiveTitleStyle,
        semanticsLabel: semanticLabel,
      ),
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Colors.transparent,
      elevation: elevation ?? 0,
      scrolledUnderElevation: 0,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
