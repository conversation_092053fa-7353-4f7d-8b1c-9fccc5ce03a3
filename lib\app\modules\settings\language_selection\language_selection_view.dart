import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/flat_list_tile.dart';
import 'language_selection_controller.dart';

class LanguageSelectionView extends GetView<LanguageSelectionController> {
  const LanguageSelectionView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Language & Region',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Auto-Detect Section
            _buildSectionHeader(context, 'Automatic Detection'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.auto_awesome,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  title: 'Auto-Detect Language',
                  subtitle: 'Use device language settings',
                  trailing: Switch(
                    value: controller.autoDetectLanguage.value,
                    onChanged: (_) => controller.toggleAutoDetectLanguage(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleAutoDetectLanguage,
                )),

            const SizedBox(height: 24),

            // Current Selection Section
            _buildSectionHeader(context, 'Current Selection'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: Text(
                        controller
                            .getLanguageFlag(controller.selectedLanguage.value),
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                  title: 'Language',
                  subtitle:
                      '${controller.selectedLanguage.value} (${controller.getLanguageNativeName(controller.selectedLanguage.value)})',
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  onTap: () => _showLanguageDialog(context),
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.location_on,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ),
                  title: 'Region',
                  subtitle: controller.selectedRegion.value,
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  onTap: controller.showRegionDialog,
                )),

            const SizedBox(height: 24),

            // Available Languages Section
            _buildSectionHeader(context, 'Available Languages'),
            const SizedBox(height: 12),
            ...controller.availableLanguages.entries.map((entry) {
              final language = entry.key;
              final languageData = entry.value;
              final isSelected = controller.selectedLanguage.value == language;

              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Obx(() => FlatListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? theme.colorScheme.primary.withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Center(
                          child: Text(
                            languageData['flag'],
                            style: const TextStyle(fontSize: 20),
                          ),
                        ),
                      ),
                      title: languageData['name'],
                      subtitle: languageData['nativeName'],
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (isSelected)
                            Icon(
                              Icons.check_circle,
                              color: theme.colorScheme.primary,
                              size: 20,
                            ),
                          const SizedBox(width: 8),
                          IconButton(
                            icon: const Icon(Icons.download, size: 20),
                            onPressed: () =>
                                controller.downloadLanguagePack(language),
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.6),
                          ),
                        ],
                      ),
                      onTap: () => controller.changeLanguage(language),
                    )),
              );
            }),

            const SizedBox(height: 24),

            // Language Pack Info Section
            _buildSectionHeader(context, 'Language Packs'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest
                    .withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'About Language Packs',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Download language packs for offline use. This ensures the app interface remains in your preferred language even without an internet connection.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.colorScheme.onSurface,
      ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    final theme = Theme.of(context);
    Get.dialog(
      AlertDialog(
        title: const Text('Select Language'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: controller.availableLanguages.length,
            itemBuilder: (context, index) {
              final entry =
                  controller.availableLanguages.entries.elementAt(index);
              final language = entry.key;
              final languageData = entry.value;

              return Obx(() => RadioListTile<String>(
                    title: Row(
                      children: [
                        Text(
                          languageData['flag'],
                          style: const TextStyle(fontSize: 20),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                languageData['name'],
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                languageData['nativeName'],
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface
                                      .withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    value: language,
                    groupValue: controller.selectedLanguage.value,
                    onChanged: (value) {
                      if (value != null) {
                        controller.changeLanguage(value);
                        Get.back();
                      }
                    },
                    activeColor: theme.colorScheme.primary,
                  ));
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
