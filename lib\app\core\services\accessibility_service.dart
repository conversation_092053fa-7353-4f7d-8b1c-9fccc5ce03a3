import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// Service for managing accessibility features and settings
class AccessibilityService extends GetxService {
  static AccessibilityService get to => Get.find();

  // Accessibility settings
  final RxBool _isScreenReaderEnabled = false.obs;
  final RxBool _isHighContrastEnabled = false.obs;
  final RxBool _isLargeTextEnabled = false.obs;
  final RxBool _isReduceMotionEnabled = false.obs;
  final RxDouble _textScaleFactor = 1.0.obs;
  final RxBool _isBoldTextEnabled = false.obs;

  // Getters
  bool get isScreenReaderEnabled => _isScreenReaderEnabled.value;
  bool get isHighContrastEnabled => _isHighContrastEnabled.value;
  bool get isLargeTextEnabled => _isLargeTextEnabled.value;
  bool get isReduceMotionEnabled => _isReduceMotionEnabled.value;
  double get textScaleFactor => _textScaleFactor.value;
  bool get isBoldTextEnabled => _isBoldTextEnabled.value;

  @override
  void onInit() {
    super.onInit();
    _initializeAccessibilitySettings();
  }

  /// Initialize accessibility settings from system
  void _initializeAccessibilitySettings() {
    // Check system accessibility settings
    _checkSystemAccessibilitySettings();

    // Listen for accessibility changes
    _setupAccessibilityListeners();
  }

  /// Check system accessibility settings
  void _checkSystemAccessibilitySettings() {
    final mediaQuery = MediaQueryData.fromView(
        WidgetsBinding.instance.platformDispatcher.views.first);

    _isScreenReaderEnabled.value = mediaQuery.accessibleNavigation;
    _isHighContrastEnabled.value = mediaQuery.highContrast;
    _isLargeTextEnabled.value = mediaQuery.textScaler.scale(1.0) > 1.2;
    _textScaleFactor.value = mediaQuery.textScaler.scale(1.0);
    _isBoldTextEnabled.value = mediaQuery.boldText;
    _isReduceMotionEnabled.value = mediaQuery.disableAnimations;
  }

  /// Setup accessibility listeners
  void _setupAccessibilityListeners() {
    // Listen for system accessibility changes
    WidgetsBinding.instance.platformDispatcher.onAccessibilityFeaturesChanged =
        () {
      _checkSystemAccessibilitySettings();
    };
  }

  /// Announce text to screen reader
  void announceToScreenReader(String message) {
    if (_isScreenReaderEnabled.value) {
      SemanticsService.announce(message, TextDirection.ltr);
    }
  }

  /// Create semantic label for widget
  String createSemanticLabel({
    required String label,
    String? hint,
    String? value,
    bool isButton = false,
    bool isSelected = false,
    bool isEnabled = true,
  }) {
    final parts = <String>[label];

    if (value != null && value.isNotEmpty) {
      parts.add(value);
    }

    if (isButton) {
      parts.add('button');
    }

    if (isSelected) {
      parts.add('selected');
    }

    if (!isEnabled) {
      parts.add('disabled');
    }

    if (hint != null && hint.isNotEmpty) {
      parts.add(hint);
    }

    return parts.join(', ');
  }

  /// Get accessible text style
  TextStyle getAccessibleTextStyle(TextStyle baseStyle) {
    var style = baseStyle;

    // Apply text scaling
    if (_textScaleFactor.value != 1.0) {
      style = style.copyWith(
        fontSize: (style.fontSize ?? 14) * _textScaleFactor.value,
      );
    }

    // Apply bold text if enabled
    if (_isBoldTextEnabled.value) {
      style = style.copyWith(
        fontWeight: FontWeight.bold,
      );
    }

    return style;
  }

  /// Get accessible color with high contrast if needed
  Color getAccessibleColor(Color color, Color backgroundColor) {
    if (!_isHighContrastEnabled.value) {
      return color;
    }

    // Calculate contrast ratio and adjust if needed
    final contrastRatio = _calculateContrastRatio(color, backgroundColor);

    if (contrastRatio < 4.5) {
      // Adjust color for better contrast
      return _adjustColorForContrast(color, backgroundColor);
    }

    return color;
  }

  /// Calculate contrast ratio between two colors
  double _calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();

    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Adjust color for better contrast
  Color _adjustColorForContrast(Color color, Color backgroundColor) {
    final backgroundLuminance = backgroundColor.computeLuminance();

    // If background is dark, make color lighter
    if (backgroundLuminance < 0.5) {
      return Color.lerp(color, Colors.white, 0.3) ?? color;
    } else {
      // If background is light, make color darker
      return Color.lerp(color, Colors.black, 0.3) ?? color;
    }
  }

  /// Get accessible animation duration
  Duration getAccessibleAnimationDuration(Duration baseDuration) {
    if (_isReduceMotionEnabled.value) {
      return Duration.zero;
    }
    return baseDuration;
  }

  /// Create accessible button
  Widget createAccessibleButton({
    required Widget child,
    required VoidCallback? onPressed,
    required String semanticLabel,
    String? semanticHint,
    bool isSelected = false,
    EdgeInsets? padding,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      enabled: onPressed != null,
      selected: isSelected,
      child: Material(
        color: backgroundColor ?? Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: padding ?? const EdgeInsets.all(16),
            child: DefaultTextStyle(
              style: getAccessibleTextStyle(
                TextStyle(color: foregroundColor),
              ),
              child: child,
            ),
          ),
        ),
      ),
    );
  }

  /// Create accessible text field
  Widget createAccessibleTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    String? errorText,
    bool obscureText = false,
    TextInputType? keyboardType,
    ValueChanged<String>? onChanged,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      textField: true,
      child: TextField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        onTap: onTap,
        style: getAccessibleTextStyle(const TextStyle()),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          errorText: errorText,
          border: const OutlineInputBorder(),
        ),
      ),
    );
  }

  /// Create accessible list tile
  Widget createAccessibleListTile({
    required Widget title,
    Widget? subtitle,
    Widget? leading,
    Widget? trailing,
    required VoidCallback? onTap,
    required String semanticLabel,
    String? semanticHint,
    bool isSelected = false,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      selected: isSelected,
      child: ListTile(
        title: DefaultTextStyle(
          style: getAccessibleTextStyle(const TextStyle()),
          child: title,
        ),
        subtitle: subtitle != null
            ? DefaultTextStyle(
                style: getAccessibleTextStyle(const TextStyle()),
                child: subtitle,
              )
            : null,
        leading: leading,
        trailing: trailing,
        onTap: onTap,
        selected: isSelected,
      ),
    );
  }

  /// Provide haptic feedback for accessibility
  void provideAccessibilityFeedback(AccessibilityFeedbackType type) {
    switch (type) {
      case AccessibilityFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
      case AccessibilityFeedbackType.impact:
        HapticFeedback.lightImpact();
        break;
      case AccessibilityFeedbackType.notification:
        HapticFeedback.mediumImpact();
        break;
      case AccessibilityFeedbackType.error:
        HapticFeedback.heavyImpact();
        break;
    }
  }

  /// Focus management for keyboard navigation
  void requestFocus(FocusNode focusNode) {
    if (_isScreenReaderEnabled.value) {
      focusNode.requestFocus();
    }
  }

  /// Create focus traversal order
  List<FocusNode> createFocusTraversalOrder(List<FocusNode> nodes) {
    return nodes;
  }

  /// Check if widget should be excluded from semantics
  bool shouldExcludeFromSemantics(Widget widget) {
    return _isReduceMotionEnabled.value && widget is AnimatedWidget;
  }

  /// Get accessibility settings summary
  Map<String, dynamic> getAccessibilitySettings() {
    return {
      'screen_reader_enabled': _isScreenReaderEnabled.value,
      'high_contrast_enabled': _isHighContrastEnabled.value,
      'large_text_enabled': _isLargeTextEnabled.value,
      'reduce_motion_enabled': _isReduceMotionEnabled.value,
      'text_scale_factor': _textScaleFactor.value,
      'bold_text_enabled': _isBoldTextEnabled.value,
    };
  }
}

/// Accessibility feedback types
enum AccessibilityFeedbackType {
  selection,
  impact,
  notification,
  error,
}

/// Accessible widget wrapper
class AccessibleWidget extends StatelessWidget {
  final Widget child;
  final String? semanticLabel;
  final String? semanticHint;
  final bool excludeSemantics;
  final bool isButton;
  final bool isSelected;
  final VoidCallback? onTap;

  const AccessibleWidget({
    super.key,
    required this.child,
    this.semanticLabel,
    this.semanticHint,
    this.excludeSemantics = false,
    this.isButton = false,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (excludeSemantics) {
      return ExcludeSemantics(child: child);
    }

    Widget widget = child;

    if (semanticLabel != null || semanticHint != null || isButton) {
      widget = Semantics(
        label: semanticLabel,
        hint: semanticHint,
        button: isButton,
        selected: isSelected,
        onTap: onTap,
        child: widget,
      );
    }

    return widget;
  }
}
