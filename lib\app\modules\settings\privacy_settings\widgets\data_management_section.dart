import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../shared/widgets/section_header.dart';
import '../../../../shared/widgets/privacy_setting_item.dart';
import '../../../../shared/widgets/flat_button.dart';
import '../privacy_settings_controller.dart';

/// Data management privacy settings section
class DataManagementSection extends GetView<PrivacySettingsController> {
  const DataManagementSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Data Management'),
        const SizedBox(height: 12),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.auto_delete,
          iconColor: Colors.cyan,
          title: 'Auto-Delete Data',
          subtitle: 'Automatically delete old data',
          value: controller.autoDeleteData.value,
          onChanged: controller.toggleAutoDeleteData,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.cloud_off,
          iconColor: Colors.grey,
          title: 'Offline Mode',
          subtitle: 'Prevent data syncing to cloud',
          value: controller.offlineMode.value,
          onChanged: controller.toggleOfflineMode,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 24),
        
        // Reset Button
        FlatButton(
          text: 'Reset All Privacy Settings',
          onPressed: controller.resetAllPrivacySettings,
          backgroundColor: Colors.orange.withValues(alpha: 0.1),
          textColor: Colors.orange,
          outlined: true,
          borderColor: Colors.orange.withValues(alpha: 0.3),
          width: double.infinity,
        ),
      ],
    );
  }
}
