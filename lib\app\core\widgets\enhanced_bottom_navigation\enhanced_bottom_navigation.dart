import 'package:flutter/material.dart';
import 'models/navigation_item.dart';
import 'controllers/animation_controller_manager.dart';
import 'widgets/navigation_item_widget.dart';
import 'widgets/floating_indicator.dart';

/// Enhanced bottom navigation with advanced animations and micro-interactions
class EnhancedBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<EnhancedBottomNavigationItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double height;
  final bool showLabels;
  final EdgeInsets? padding;
  final double borderRadius;
  final bool enableHapticFeedback;
  final bool enableRippleEffect;
  final bool enableFloatingIndicator;

  const EnhancedBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.height = 72,
    this.showLabels = true,
    this.padding,
    this.borderRadius = 16,
    this.enableHapticFeedback = true,
    this.enableRippleEffect = true,
    this.enableFloatingIndicator = true,
  });

  @override
  State<EnhancedBottomNavigation> createState() =>
      _EnhancedBottomNavigationState();
}

class _EnhancedBottomNavigationState extends State<EnhancedBottomNavigation>
    with TickerProviderStateMixin {
  late final AnimationControllerManager _animationManager;

  @override
  void initState() {
    super.initState();
    _animationManager = AnimationControllerManager();
    _animationManager.initialize(this);
  }

  @override
  void dispose() {
    _animationManager.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(EnhancedBottomNavigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _animationManager.animateToIndex(widget.currentIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedColor = widget.selectedItemColor ?? theme.colorScheme.primary;
    final unselectedColor = widget.unselectedItemColor ??
        theme.colorScheme.onSurface.withValues(alpha: 0.6);
    final backgroundColor = widget.backgroundColor ??
        theme.colorScheme.surface.withValues(alpha: 0.95);

    return Container(
      height: widget.height,
      margin: widget.padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Floating indicator
          FloatingIndicator(
            currentIndex: widget.currentIndex,
            itemCount: widget.items.length,
            color: selectedColor,
            enabled: widget.enableFloatingIndicator,
            animationManager: _animationManager,
          ),

          // Navigation items
          Row(
            children: widget.items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == widget.currentIndex;

              return NavigationItemWidget(
                item: item,
                isSelected: isSelected,
                onTap: () => widget.onTap(index),
                selectedColor: selectedColor,
                unselectedColor: unselectedColor,
                showLabel: widget.showLabels,
                enableHapticFeedback: widget.enableHapticFeedback,
                enableRippleEffect: widget.enableRippleEffect,
                animationManager: _animationManager,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
