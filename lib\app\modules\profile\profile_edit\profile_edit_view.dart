import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/widgets/animated_background.dart';
import '../../../core/widgets/cards/flat_card.dart';
import '../../../core/widgets/glass_text_field.dart';
import '../../../core/widgets/gradient_button.dart';
import '../../../core/theme/app_theme.dart';
import 'profile_edit_controller.dart';

class ProfileEditView extends GetView<ProfileEditController> {
  const ProfileEditView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: AnimatedBackground(
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: controller.showDiscardDialog,
                      icon: Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Edit Profile',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 48), // Balance the back button
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: controller.formKey,
                    child: Column(
                      children: [
                        // Profile Image Section
                        FlatCard(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            children: [
                              Text(
                                'Profile Photo',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Profile Image
                              Obx(() => GestureDetector(
                                    onTap: controller.selectProfileImage,
                                    child: Container(
                                      width: 120,
                                      height: 120,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withValues(alpha: 0.3),
                                          width: 2,
                                        ),
                                        color: Colors.grey[100],
                                      ),
                                      child: ClipOval(
                                        child: controller.hasProfileImage
                                            ? controller.getProfileImageWidget()
                                            : const Icon(
                                                Icons.add_a_photo,
                                                size: 40,
                                                color: Colors.grey,
                                              ),
                                      ),
                                    ),
                                  )),

                              const SizedBox(height: 16),

                              // Image Actions
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  TextButton.icon(
                                    onPressed: controller.selectProfileImage,
                                    icon: const Icon(Icons.camera_alt),
                                    label: const Text('Change Photo'),
                                    style: TextButton.styleFrom(
                                      foregroundColor:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                  Obx(() => controller.hasProfileImage
                                      ? TextButton.icon(
                                          onPressed:
                                              controller.removeProfileImage,
                                          icon: const Icon(Icons.delete),
                                          label: const Text('Remove'),
                                          style: TextButton.styleFrom(
                                            foregroundColor: Colors.red,
                                          ),
                                        )
                                      : const SizedBox.shrink()),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Personal Information
                        FlatCard(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Personal Information',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 20),

                              // Name Field
                              FlatTextField(
                                controller: controller.nameController,
                                hintText: 'Enter your full name',
                                labelText: 'Full Name',
                                keyboardType: TextInputType.name,
                                textCapitalization: TextCapitalization.words,
                                prefixIcon: const Icon(Icons.person_outline),
                                validator: controller.validateName,
                              ),

                              const SizedBox(height: 16),

                              // Email Field
                              FlatTextField(
                                controller: controller.emailController,
                                hintText: 'Enter your email',
                                labelText: 'Email',
                                keyboardType: TextInputType.emailAddress,
                                prefixIcon: const Icon(Icons.email_outlined),
                                validator: controller.validateEmail,
                              ),

                              const SizedBox(height: 16),

                              // Phone Field
                              FlatTextField(
                                controller: controller.phoneController,
                                hintText: 'Enter your phone number',
                                labelText: 'Phone Number (Optional)',
                                keyboardType: TextInputType.phone,
                                prefixIcon: const Icon(Icons.phone_outlined),
                                validator: controller.validatePhone,
                              ),

                              const SizedBox(height: 16),

                              // Bio Field
                              FlatTextField(
                                controller: controller.bioController,
                                hintText: 'Tell us about yourself',
                                labelText: 'Bio (Optional)',
                                keyboardType: TextInputType.multiline,
                                textCapitalization:
                                    TextCapitalization.sentences,
                                prefixIcon: const Icon(Icons.info_outline),
                                maxLines: 3,
                                maxLength: 200,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Save Button
                        Obx(() => GradientButton(
                              text: 'Save Changes',
                              onPressed: controller.saveProfile,
                              isLoading: controller.isLoading.value,
                              gradient: Theme.of(context)
                                  .auroraGradients
                                  .primaryAuroraGradient,
                            )),

                        const SizedBox(height: 16),

                        // Cancel Button
                        OutlinedButton(
                          onPressed: controller.showDiscardDialog,
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            side: BorderSide(
                                color: Theme.of(context).colorScheme.outline),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
