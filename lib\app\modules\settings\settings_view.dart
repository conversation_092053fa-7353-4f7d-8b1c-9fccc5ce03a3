import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/flat_list_tile.dart';
import '../../core/widgets/flat_button.dart';
import 'settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Settings',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Appearance Section
            _buildSectionHeader(context, 'Appearance'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      controller.isDarkMode.value
                          ? Icons.dark_mode
                          : Icons.light_mode,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  title: 'Dark Mode',
                  subtitle:
                      controller.isDarkMode.value ? 'Enabled' : 'Disabled',
                  trailing: Switch(
                    value: controller.isDarkMode.value,
                    onChanged: (_) => controller.toggleDarkMode(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleDarkMode,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.palette,
                      color: Colors.purple,
                      size: 20,
                    ),
                  ),
                  title: 'Theme',
                  subtitle: controller.selectedTheme.value.name
                      .replaceAll(RegExp(r'([A-Z])'), ' \$1')
                      .trim(),
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  onTap: controller.showThemeSelection,
                )),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: theme.colorScheme.secondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.text_fields,
                  color: theme.colorScheme.secondary,
                  size: 20,
                ),
              ),
              title: 'Font Size',
              subtitle: 'Adjust text size',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: () => _showFontSizeDialog(context),
            ),

            const SizedBox(height: 24),

            // Notifications Section
            _buildSectionHeader(context, 'Notifications'),
            const SizedBox(height: 12),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.notifications,
                  color: Colors.orange,
                  size: 20,
                ),
              ),
              title: 'Notification Preferences',
              subtitle: 'Manage all notification settings',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.showNotificationPreferences,
            ),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.notifications_active,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  title: 'Quick Toggle',
                  subtitle: controller.notificationsEnabled.value
                      ? 'Notifications enabled'
                      : 'Notifications disabled',
                  trailing: Switch(
                    value: controller.notificationsEnabled.value,
                    onChanged: (_) => controller.toggleNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleNotifications,
                )),

            const SizedBox(height: 24),

            // Security Section
            _buildSectionHeader(context, 'Security'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.fingerprint,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  title: 'Biometric Authentication',
                  subtitle: controller.biometricEnabled.value
                      ? 'Enabled'
                      : 'Disabled',
                  trailing: Switch(
                    value: controller.biometricEnabled.value,
                    onChanged: (_) => controller.toggleBiometric(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleBiometric,
                )),

            const SizedBox(height: 24),

            // Data & Storage Section
            _buildSectionHeader(context, 'Data & Storage'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.backup,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  title: 'Auto Backup',
                  subtitle:
                      controller.autoBackup.value ? 'Enabled' : 'Disabled',
                  trailing: Switch(
                    value: controller.autoBackup.value,
                    onChanged: (_) => controller.toggleAutoBackup(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleAutoBackup,
                )),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.clear_all,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              title: 'Clear Cache',
              subtitle: 'Free up storage space',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.clearCache,
            ),

            const SizedBox(height: 24),

            // General Section
            _buildSectionHeader(context, 'General'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.language,
                      color: Colors.purple,
                      size: 20,
                    ),
                  ),
                  title: 'Language & Region',
                  subtitle: controller.selectedLanguage.value,
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  onTap: controller.showLanguageSelection,
                )),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.teal.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.info,
                  color: Colors.teal,
                  size: 20,
                ),
              ),
              title: 'About',
              subtitle: 'App version and info',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.showAbout,
            ),

            const SizedBox(height: 24),

            // Account & Privacy Section
            _buildSectionHeader(context, 'Account & Privacy'),
            const SizedBox(height: 12),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.indigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.account_circle,
                  color: Colors.indigo,
                  size: 20,
                ),
              ),
              title: 'Account Management',
              subtitle: 'Security, login settings, data export',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.showAccountManagement,
            ),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.privacy_tip,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              title: 'Privacy Settings',
              subtitle: 'Data collection, permissions, retention',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onTap: controller.showPrivacySettings,
            ),

            const SizedBox(height: 32),

            // Reset Button
            FlatButton(
              text: 'Reset All Settings',
              onPressed: controller.resetSettings,
              backgroundColor: Colors.red.withValues(alpha: 0.1),
              textColor: Colors.red,
              outlined: true,
              borderColor: Colors.red.withValues(alpha: 0.3),
              width: double.infinity,
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
    );
  }

  void _showFontSizeDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('Font Size'),
        content: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Sample Text',
                  style: TextStyle(fontSize: 16 * controller.fontSize.value),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: controller.fontSize.value,
                  min: 0.8,
                  max: 1.4,
                  divisions: 6,
                  label: '${(controller.fontSize.value * 100).round()}%',
                  onChanged: controller.changeFontSize,
                ),
              ],
            )),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }
}
