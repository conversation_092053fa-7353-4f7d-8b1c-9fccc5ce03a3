import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../shared/widgets/section_header.dart';
import '../../../../shared/widgets/privacy_setting_item.dart';
import '../privacy_settings_controller.dart';

/// Data collection privacy settings section
class DataCollectionSection extends GetView<PrivacySettingsController> {
  const DataCollectionSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Data Collection'),
        const SizedBox(height: 12),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.data_usage,
          iconColor: Colors.blue,
          title: 'Data Collection',
          subtitle: 'Allow collection of usage data',
          value: controller.dataCollection.value,
          onChanged: controller.toggleDataCollection,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.analytics,
          iconColor: Colors.orange,
          title: 'Analytics Tracking',
          subtitle: 'Help improve the app with usage analytics',
          value: controller.analyticsTracking.value,
          onChanged: controller.toggleAnalyticsTracking,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.bug_report,
          iconColor: Colors.red,
          title: 'Crash Reporting',
          subtitle: 'Send crash reports to help fix issues',
          value: controller.crashReporting.value,
          onChanged: controller.toggleCrashReporting,
          activeColor: theme.colorScheme.primary,
        )),
      ],
    );
  }
}
