import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nexed_mini/app/core/components/index.dart';

void main() {
  group('Unified Components Tests', () {
    testWidgets('UnifiedButton renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: UnifiedButton.filled(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.text('Test Button'), findsOneWidget);
      expect(find.byType(UnifiedButton), findsOneWidget);
    });

    testWidgets('UnifiedButton variants render correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: SingleChildScrollView(
              child: Column(
                children: [
                  UnifiedButton.filled(text: 'Filled', onPressed: () {}),
                  UnifiedButton.outlined(text: 'Outlined', onPressed: () {}),
                  UnifiedButton.text(text: 'Text', onPressed: () {}),
                  UnifiedButton.gradient(text: 'Gradient', onPressed: () {}),
                ],
              ),
            ),
          ),
        ),
      );

      expect(find.text('Filled'), findsOneWidget);
      expect(find.text('Outlined'), findsOneWidget);
      expect(find.text('Text'), findsOneWidget);
      expect(find.text('Gradient'), findsOneWidget);
      expect(find.byType(UnifiedButton), findsNWidgets(4));
    });

    testWidgets('UnifiedIconButton renders correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UnifiedIconButton.filled(
              icon: Icons.add,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.byType(UnifiedIconButton), findsOneWidget);
    });

    testWidgets('UnifiedCard renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UnifiedCard.elevated(
              child: Text('Test Card'),
            ),
          ),
        ),
      );

      expect(find.text('Test Card'), findsOneWidget);
      expect(find.byType(UnifiedCard), findsOneWidget);
    });

    testWidgets('UnifiedCard variants render correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                UnifiedCard.elevated(child: Text('Elevated')),
                UnifiedCard.filled(child: Text('Filled')),
                UnifiedCard.outlined(child: Text('Outlined')),
                UnifiedCard.flat(child: Text('Flat')),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Elevated'), findsOneWidget);
      expect(find.text('Filled'), findsOneWidget);
      expect(find.text('Outlined'), findsOneWidget);
      expect(find.text('Flat'), findsOneWidget);
      expect(find.byType(UnifiedCard), findsNWidgets(4));
    });

    testWidgets('Button tap callbacks work', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UnifiedButton.filled(
              text: 'Tap Me',
              onPressed: () {
                tapped = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Tap Me'));
      expect(tapped, isTrue);
    });

    testWidgets('Card tap callbacks work', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UnifiedCard.elevated(
              onTap: () {
                tapped = true;
              },
              child: Text('Tap Me'),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Tap Me'));
      expect(tapped, isTrue);
    });

    testWidgets('Button loading state works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UnifiedButton.filled(
              text: 'Loading',
              loading: true,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Button with icon renders correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UnifiedButton.filled(
              text: 'Save',
              icon: Icons.save,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.text('Save'), findsOneWidget);
      expect(find.byIcon(Icons.save), findsOneWidget);
    });

    test('AppColorsTheme provides correct colors', () {
      const colors = AppColorsTheme(
        textPrimary: Colors.black,
        textSecondary: Colors.grey,
        success: Colors.green,
        warning: Colors.orange,
        error: Colors.red,
        info: Colors.blue,
      );

      expect(colors.textPrimary, equals(Colors.black));
      expect(colors.textSecondary, equals(Colors.grey));
      expect(colors.success, equals(Colors.green));
      expect(colors.warning, equals(Colors.orange));
      expect(colors.error, equals(Colors.red));
      expect(colors.info, equals(Colors.blue));
    });

    testWidgets('Theme optimization works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: Builder(
              builder: (context) {
                final colors = AppColors.getThemeColors(context);
                return Container(
                  color: colors.textPrimary,
                  child: Text(
                    'Themed Text',
                    style: TextStyle(color: colors.textSecondary),
                  ),
                );
              },
            ),
          ),
        ),
      );

      expect(find.text('Themed Text'), findsOneWidget);
    });
  });
}
