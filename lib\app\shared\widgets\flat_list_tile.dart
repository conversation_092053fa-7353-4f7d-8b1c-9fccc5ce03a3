import 'package:flutter/material.dart';

/// A customizable list tile widget with flat design
class FlatListTile extends StatelessWidget {
  const FlatListTile({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.backgroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.enabled = true,
    this.dense = false,
    this.contentPadding,
    this.titleStyle,
    this.subtitleStyle,
    this.leadingSize,
    this.trailingSize,
  });

  final Widget? leading;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool enabled;
  final bool dense;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final double? leadingSize;
  final double? trailingSize;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? EdgeInsets.zero,
      padding: padding ?? EdgeInsets.zero,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: enabled ? onTap : null,
          borderRadius: borderRadius ?? BorderRadius.circular(12),
          child: Padding(
            padding: contentPadding ?? 
                EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: dense ? 8 : 12,
                ),
            child: Row(
              children: [
                if (leading != null) ...[
                  SizedBox(
                    width: leadingSize ?? 40,
                    height: leadingSize ?? 40,
                    child: leading,
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: titleStyle ?? 
                            theme.textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: enabled 
                                  ? theme.colorScheme.onSurface
                                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          subtitle!,
                          style: subtitleStyle ?? 
                              theme.textTheme.bodyMedium?.copyWith(
                                color: enabled
                                    ? theme.colorScheme.onSurface.withValues(alpha: 0.7)
                                    : theme.colorScheme.onSurface.withValues(alpha: 0.4),
                              ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (trailing != null) ...[
                  const SizedBox(width: 12),
                  SizedBox(
                    width: trailingSize,
                    height: trailingSize,
                    child: trailing,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// A switch list tile with flat design
class FlatSwitchListTile extends StatelessWidget {
  const FlatSwitchListTile({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    required this.value,
    required this.onChanged,
    this.backgroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.enabled = true,
    this.dense = false,
    this.contentPadding,
    this.titleStyle,
    this.subtitleStyle,
    this.activeColor,
  });

  final Widget? leading;
  final String title;
  final String? subtitle;
  final bool value;
  final ValueChanged<bool>? onChanged;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool enabled;
  final bool dense;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final Color? activeColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? EdgeInsets.zero,
      padding: padding ?? EdgeInsets.zero,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: Padding(
        padding: contentPadding ?? 
            EdgeInsets.symmetric(
              horizontal: 16,
              vertical: dense ? 8 : 12,
            ),
        child: Row(
          children: [
            if (leading != null) ...[
              SizedBox(
                width: 40,
                height: 40,
                child: leading,
              ),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: titleStyle ?? 
                        theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: enabled 
                              ? theme.colorScheme.onSurface
                              : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle!,
                      style: subtitleStyle ?? 
                          theme.textTheme.bodyMedium?.copyWith(
                            color: enabled
                                ? theme.colorScheme.onSurface.withValues(alpha: 0.7)
                                : theme.colorScheme.onSurface.withValues(alpha: 0.4),
                          ),
                    ),
                  ],
                ],
              ),
            ),
            const SizedBox(width: 12),
            Switch(
              value: value,
              onChanged: enabled ? onChanged : null,
              activeColor: activeColor ?? theme.colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }
}
