import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/flat_list_tile.dart';
import 'notification_preferences_controller.dart';

class NotificationPreferencesView
    extends GetView<NotificationPreferencesController> {
  const NotificationPreferencesView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Notification Preferences',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // General Notifications Section
            _buildSectionHeader(context, 'General'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.notifications,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  title: 'Push Notifications',
                  subtitle: 'Receive notifications on this device',
                  trailing: Switch(
                    value: controller.pushNotifications.value,
                    onChanged: (_) => controller.togglePushNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.togglePushNotifications,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.email,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  title: 'Email Notifications',
                  subtitle: 'Receive notifications via email',
                  trailing: Switch(
                    value: controller.emailNotifications.value,
                    onChanged: (_) => controller.toggleEmailNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleEmailNotifications,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.sms,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ),
                  title: 'SMS Notifications',
                  subtitle: 'Receive notifications via SMS',
                  trailing: Switch(
                    value: controller.smsNotifications.value,
                    onChanged: (_) => controller.toggleSmsNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleSmsNotifications,
                )),

            const SizedBox(height: 24),

            // Notification Types Section
            _buildSectionHeader(context, 'Notification Types'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.security,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                  title: 'Security Alerts',
                  subtitle: 'Login attempts, password changes',
                  trailing: Switch(
                    value: controller.securityNotifications.value,
                    onChanged: (_) => controller.toggleSecurityNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleSecurityNotifications,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.purple,
                      size: 20,
                    ),
                  ),
                  title: 'Transaction Updates',
                  subtitle: 'Payment confirmations, receipts',
                  trailing: Switch(
                    value: controller.transactionNotifications.value,
                    onChanged: (_) =>
                        controller.toggleTransactionNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleTransactionNotifications,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.teal.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.article,
                      color: Colors.teal,
                      size: 20,
                    ),
                  ),
                  title: 'News & Updates',
                  subtitle: 'App updates, feature announcements',
                  trailing: Switch(
                    value: controller.newsNotifications.value,
                    onChanged: (_) => controller.toggleNewsNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleNewsNotifications,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.pink.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.local_offer,
                      color: Colors.pink,
                      size: 20,
                    ),
                  ),
                  title: 'Promotional Offers',
                  subtitle: 'Special deals and discounts',
                  trailing: Switch(
                    value: controller.promotionalNotifications.value,
                    onChanged: (_) =>
                        controller.togglePromotionalNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.togglePromotionalNotifications,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.indigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.campaign,
                      color: Colors.indigo,
                      size: 20,
                    ),
                  ),
                  title: 'Marketing Communications',
                  subtitle: 'Product updates, newsletters',
                  trailing: Switch(
                    value: controller.marketingNotifications.value,
                    onChanged: (_) => controller.toggleMarketingNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleMarketingNotifications,
                )),

            const SizedBox(height: 24),

            // Do Not Disturb Section
            _buildSectionHeader(context, 'Do Not Disturb'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.do_not_disturb,
                      color: Colors.grey,
                      size: 20,
                    ),
                  ),
                  title: 'Do Not Disturb',
                  subtitle: controller.doNotDisturb.value
                      ? 'Active from ${controller.doNotDisturbStart.value} to ${controller.doNotDisturbEnd.value}'
                      : 'Disabled',
                  trailing: Switch(
                    value: controller.doNotDisturb.value,
                    onChanged: (_) => controller.toggleDoNotDisturb(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleDoNotDisturb,
                )),

            if (controller.doNotDisturb.value) ...[
              const SizedBox(height: 8),
              Obx(() => FlatListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.schedule,
                        color: Colors.blue,
                        size: 20,
                      ),
                    ),
                    title: 'Start Time',
                    subtitle: controller.doNotDisturbStart.value,
                    trailing: Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    onTap: () => controller.selectTime('start'),
                  )),
              const SizedBox(height: 8),
              Obx(() => FlatListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.schedule,
                        color: Colors.orange,
                        size: 20,
                      ),
                    ),
                    title: 'End Time',
                    subtitle: controller.doNotDisturbEnd.value,
                    trailing: Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    onTap: () => controller.selectTime('end'),
                  )),
            ],

            const SizedBox(height: 24),

            // Sound & Vibration Section
            _buildSectionHeader(context, 'Sound & Vibration'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.volume_up,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  title: 'Notification Sound',
                  subtitle: 'Play sound for notifications',
                  trailing: Switch(
                    value: controller.notificationSound.value,
                    onChanged: (_) => controller.toggleNotificationSound(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleNotificationSound,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.vibration,
                      color: Colors.purple,
                      size: 20,
                    ),
                  ),
                  title: 'Vibration',
                  subtitle: 'Vibrate for notifications',
                  trailing: Switch(
                    value: controller.vibration.value,
                    onChanged: (_) => controller.toggleVibration(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleVibration,
                )),

            const SizedBox(height: 8),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.teal.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.music_note,
                      color: Colors.teal,
                      size: 20,
                    ),
                  ),
                  title: 'Notification Tone',
                  subtitle: controller.selectedTone.value,
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  onTap: () => _showToneDialog(context),
                )),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.colorScheme.onSurface,
      ),
    );
  }

  void _showToneDialog(BuildContext context) {
    final theme = Theme.of(context);
    Get.dialog(
      AlertDialog(
        title: const Text('Select Notification Tone'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: controller.availableTones.length,
            itemBuilder: (context, index) {
              final tone = controller.availableTones[index];
              return Obx(() => RadioListTile<String>(
                    title: Text(tone),
                    value: tone,
                    groupValue: controller.selectedTone.value,
                    onChanged: (value) {
                      if (value != null) {
                        controller.changeNotificationTone(value);
                        Get.back();
                      }
                    },
                    activeColor: theme.colorScheme.primary,
                  ));
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
