import 'package:flutter/material.dart';

/// A customizable flat button widget
class FlatButton extends StatelessWidget {
  const FlatButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.outlined = false,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.icon,
    this.iconSize,
    this.iconSpacing = 8.0,
    this.textStyle,
    this.elevation = 0.0,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
  });

  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final bool outlined;
  final Color? borderColor;
  final double borderWidth;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final IconData? icon;
  final double? iconSize;
  final double iconSpacing;
  final TextStyle? textStyle;
  final double elevation;
  final bool enabled;
  final bool loading;
  final Color? loadingColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = enabled && !loading && onPressed != null;
    
    final effectiveBackgroundColor = backgroundColor ?? 
        (outlined ? Colors.transparent : theme.colorScheme.primary);
    
    final effectiveTextColor = textColor ?? 
        (outlined 
            ? theme.colorScheme.primary 
            : theme.colorScheme.onPrimary);
    
    final effectiveBorderColor = borderColor ?? 
        (outlined ? theme.colorScheme.primary : Colors.transparent);

    return Container(
      margin: margin ?? EdgeInsets.zero,
      width: width,
      height: height ?? 48,
      child: Material(
        color: isEnabled 
            ? effectiveBackgroundColor 
            : effectiveBackgroundColor.withValues(alpha: 0.6),
        elevation: outlined ? 0 : elevation,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: borderRadius ?? BorderRadius.circular(12),
          child: Container(
            decoration: outlined ? BoxDecoration(
              border: Border.all(
                color: isEnabled 
                    ? effectiveBorderColor 
                    : effectiveBorderColor.withValues(alpha: 0.6),
                width: borderWidth,
              ),
              borderRadius: borderRadius ?? BorderRadius.circular(12),
            ) : null,
            padding: padding ?? const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 12,
            ),
            child: loading
                ? Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          loadingColor ?? effectiveTextColor,
                        ),
                      ),
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (icon != null) ...[
                        Icon(
                          icon,
                          size: iconSize ?? 20,
                          color: isEnabled 
                              ? effectiveTextColor 
                              : effectiveTextColor.withValues(alpha: 0.6),
                        ),
                        SizedBox(width: iconSpacing),
                      ],
                      Flexible(
                        child: Text(
                          text,
                          style: textStyle ?? theme.textTheme.labelLarge?.copyWith(
                            color: isEnabled 
                                ? effectiveTextColor 
                                : effectiveTextColor.withValues(alpha: 0.6),
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}

/// A small flat button variant
class FlatButtonSmall extends StatelessWidget {
  const FlatButtonSmall({
    super.key,
    required this.text,
    required this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.outlined = false,
    this.borderColor,
    this.icon,
    this.enabled = true,
  });

  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final bool outlined;
  final Color? borderColor;
  final IconData? icon;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return FlatButton(
      text: text,
      onPressed: onPressed,
      backgroundColor: backgroundColor,
      textColor: textColor,
      outlined: outlined,
      borderColor: borderColor,
      icon: icon,
      enabled: enabled,
      height: 36,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      borderRadius: BorderRadius.circular(8),
      iconSize: 16,
      iconSpacing: 6,
    );
  }
}

/// An icon-only flat button
class FlatIconButton extends StatelessWidget {
  const FlatIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.outlined = false,
    this.borderColor,
    this.size = 48,
    this.iconSize,
    this.borderRadius,
    this.enabled = true,
    this.tooltip,
  });

  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final bool outlined;
  final Color? borderColor;
  final double size;
  final double? iconSize;
  final BorderRadius? borderRadius;
  final bool enabled;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = enabled && onPressed != null;
    
    final effectiveBackgroundColor = backgroundColor ?? 
        (outlined ? Colors.transparent : theme.colorScheme.primary);
    
    final effectiveIconColor = iconColor ?? 
        (outlined 
            ? theme.colorScheme.primary 
            : theme.colorScheme.onPrimary);
    
    final effectiveBorderColor = borderColor ?? 
        (outlined ? theme.colorScheme.primary : Colors.transparent);

    final button = Container(
      width: size,
      height: size,
      decoration: outlined ? BoxDecoration(
        border: Border.all(
          color: isEnabled 
              ? effectiveBorderColor 
              : effectiveBorderColor.withValues(alpha: 0.6),
          width: 1.0,
        ),
        borderRadius: borderRadius ?? BorderRadius.circular(size / 4),
      ) : null,
      child: Material(
        color: isEnabled 
            ? effectiveBackgroundColor 
            : effectiveBackgroundColor.withValues(alpha: 0.6),
        borderRadius: borderRadius ?? BorderRadius.circular(size / 4),
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: borderRadius ?? BorderRadius.circular(size / 4),
          child: Icon(
            icon,
            size: iconSize ?? size * 0.5,
            color: isEnabled 
                ? effectiveIconColor 
                : effectiveIconColor.withValues(alpha: 0.6),
          ),
        ),
      ),
    );

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }
}
