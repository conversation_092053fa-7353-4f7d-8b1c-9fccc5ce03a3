import 'package:flutter/material.dart';
import '../../../core/utils/responsive_utils.dart';

/// Responsive and accessible list tile
class ResponsiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool dense;
  final String? semanticLabel;
  final String? semanticHint;

  const ResponsiveListTile({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.dense = false,
    this.semanticLabel,
    this.semanticHint,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = ResponsiveUtils.responsive(
      context,
      mobile: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      tablet: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      desktop: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    );

    Widget listTile = ListTile(
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      dense: dense,
      contentPadding: responsivePadding,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );

    // Add accessibility features
    if (semanticLabel != null || semanticHint != null) {
      listTile = Semantics(
        label: semanticLabel,
        hint: semanticHint,
        button: onTap != null,
        child: listTile,
      );
    }

    return listTile;
  }
}
