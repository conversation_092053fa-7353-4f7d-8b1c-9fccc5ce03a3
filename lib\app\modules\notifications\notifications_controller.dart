import 'package:get/get.dart';

class NotificationsController extends GetxController {
  final RxList<NotificationItem> notifications = <NotificationItem>[].obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadNotifications();
  }

  void _loadNotifications() {
    // Mock notification data
    notifications.value = [
      NotificationItem(
        id: '1',
        title: 'Assignment Due Tomorrow',
        message:
            'Math homework is due tomorrow at 9:00 AM. Don\'t forget to submit!',
        time: DateTime.now().subtract(const Duration(minutes: 30)),
        type: NotificationType.assignment,
        isRead: false,
      ),
      NotificationItem(
        id: '2',
        title: 'Cafeteria Menu Updated',
        message:
            'New items added to today\'s lunch menu. Check out the delicious options!',
        time: DateTime.now().subtract(const Duration(hours: 2)),
        type: NotificationType.cafeteria,
        isRead: false,
      ),
      NotificationItem(
        id: '3',
        title: 'Library Book Due',
        message:
            'Your borrowed book "Introduction to Physics" is due in 2 days.',
        time: DateTime.now().subtract(const Duration(hours: 4)),
        type: NotificationType.library,
        isRead: true,
      ),
      NotificationItem(
        id: '4',
        title: 'Sports Event Registration',
        message:
            'Registration for the annual sports day is now open. Sign up before Friday!',
        time: DateTime.now().subtract(const Duration(days: 1)),
        type: NotificationType.sports,
        isRead: true,
      ),
      NotificationItem(
        id: '5',
        title: 'Grade Published',
        message:
            'Your Science test grade has been published. Check your results now.',
        time: DateTime.now().subtract(const Duration(days: 2)),
        type: NotificationType.grade,
        isRead: false,
      ),
      NotificationItem(
        id: '6',
        title: 'School Holiday Notice',
        message: 'School will be closed next Monday for a public holiday.',
        time: DateTime.now().subtract(const Duration(days: 3)),
        type: NotificationType.announcement,
        isRead: true,
      ),
    ];
  }

  void markAsRead(String notificationId) {
    final index = notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      notifications[index] = notifications[index].copyWith(isRead: true);
      notifications.refresh();
    }
  }

  void markAllAsRead() {
    for (int i = 0; i < notifications.length; i++) {
      notifications[i] = notifications[i].copyWith(isRead: true);
    }
    notifications.refresh();

    Get.snackbar(
      'Success',
      'All notifications marked as read',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void deleteNotification(String notificationId) {
    notifications.removeWhere((n) => n.id == notificationId);

    Get.snackbar(
      'Deleted',
      'Notification deleted successfully',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void openNotification(NotificationItem notification) {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }

    Get.snackbar(
      notification.title,
      notification.message,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }

  int get unreadCount => notifications.where((n) => !n.isRead).length;
}

class NotificationItem {
  final String id;
  final String title;
  final String message;
  final DateTime time;
  final NotificationType type;
  final bool isRead;

  NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.time,
    required this.type,
    required this.isRead,
  });

  NotificationItem copyWith({
    String? id,
    String? title,
    String? message,
    DateTime? time,
    NotificationType? type,
    bool? isRead,
  }) {
    return NotificationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      time: time ?? this.time,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
    );
  }
}

enum NotificationType {
  assignment,
  cafeteria,
  library,
  sports,
  grade,
  announcement,
}
