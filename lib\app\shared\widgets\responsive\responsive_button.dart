import 'package:flutter/material.dart';
import '../../../core/utils/responsive_utils.dart';

/// Responsive and accessible button
class ResponsiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final Widget? icon;
  final bool isLoading;
  final String? semanticLabel;
  final String? semanticHint;

  const ResponsiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isLoading = false,
    this.semanticLabel,
    this.semanticHint,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final responsiveStyle = style ??
        ElevatedButton.styleFrom(
          padding: ResponsiveUtils.responsive(
            context,
            mobile: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            tablet: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
            desktop: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          ),
          textStyle: ResponsiveUtils.responsive(
            context,
            mobile: theme.textTheme.bodyMedium,
            tablet: theme.textTheme.bodyLarge,
            desktop: theme.textTheme.titleMedium,
          ),
        );

    Widget button;

    if (icon != null) {
      button = ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        style: responsiveStyle,
        icon: isLoading
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.onPrimary,
                  ),
                ),
              )
            : icon!,
        label: Text(text),
      );
    } else {
      button = ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: responsiveStyle,
        child: isLoading
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.onPrimary,
                  ),
                ),
              )
            : Text(text),
      );
    }

    // Add accessibility features
    if (semanticLabel != null || semanticHint != null) {
      button = Semantics(
        label: semanticLabel ?? text,
        hint: semanticHint,
        button: true,
        enabled: onPressed != null && !isLoading,
        child: button,
      );
    }

    return button;
  }
}
