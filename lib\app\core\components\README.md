# Nexed Mini Component Library

A unified, reusable component system for the Nexed Mini Flutter application.

## Overview

This component library consolidates multiple similar widget implementations into unified, configurable components that:

- ✅ Reduce code duplication
- ✅ Provide consistent theming
- ✅ Optimize performance
- ✅ Improve maintainability
- ✅ Follow Material 3 design principles

## Components

### 🔘 Unified Button System

Replaces multiple button implementations with a single, configurable system:

- **UnifiedButton**: Main button component with variants (filled, outlined, text, gradient)
- **UnifiedIconButton**: Icon-only button component

**Replaced Components:**
- `FlatButton` (shared/widgets)
- `FlatButton` (core/widgets) 
- `GradientButton`
- `ResponsiveButton`
- `CompactFlatButton`
- `FlatIconButton`

### 🃏 Unified Card System

Consolidates card implementations into a single, flexible component:

- **UnifiedCard**: Main card component with variants (elevated, filled, outlined, flat)

**Replaced Components:**
- `BaseCard`
- `ModernCard`
- `FlatCard`
- `FeatureCard` (partially)

### 🎨 Optimized Theme System

Enhanced theme access patterns:

- **AppColorsTheme**: Batch color access to reduce Theme.of(context) calls
- **Unified theme building**: Reduced code duplication in theme generation

## Usage

### Import the Component Library

```dart
import 'package:nexed_mini/app/core/components/index.dart';
```

### Button Examples

```dart
// Basic buttons
UnifiedButton.filled(text: 'Submit', onPressed: () {})
UnifiedButton.outlined(text: 'Cancel', onPressed: () {})
UnifiedButton.text(text: 'Skip', onPressed: () {})
UnifiedButton.gradient(text: 'Get Started', onPressed: () {})

// With icons
UnifiedButton.filled(
  text: 'Save',
  icon: Icons.save,
  onPressed: () {},
)

// Icon-only buttons
UnifiedIconButton.filled(icon: Icons.add, onPressed: () {})
UnifiedIconButton.outlined(icon: Icons.edit, onPressed: () {})
```

### Card Examples

```dart
// Basic cards
UnifiedCard.elevated(child: Text('Content'))
UnifiedCard.filled(child: Text('Content'))
UnifiedCard.outlined(child: Text('Content'))
UnifiedCard.flat(child: Text('Content'))

// With interaction
UnifiedCard.elevated(
  onTap: () {},
  child: Text('Tappable card'),
)
```

### Theme Optimization

```dart
Widget build(BuildContext context) {
  final theme = Theme.of(context);
  final colors = AppColors.getThemeColors(context);
  
  // Use colors.textPrimary, colors.success, etc.
  // instead of multiple Theme.of(context) calls
}
```

## Migration Guide

### Button Migration

| Old Component | New Component |
|---------------|---------------|
| `FlatButton(outlined: true)` | `UnifiedButton.outlined()` |
| `FlatButton(outlined: false)` | `UnifiedButton.filled()` |
| `GradientButton()` | `UnifiedButton.gradient()` |
| `FlatIconButton()` | `UnifiedIconButton.filled()` |

### Card Migration

| Old Component | New Component |
|---------------|---------------|
| `BaseCard()` | `UnifiedCard.elevated()` |
| `ModernCard()` | `UnifiedCard.elevated()` |
| `FlatCard()` | `UnifiedCard.flat()` |

## Benefits

### Before Refactoring
- 5+ button implementations with overlapping functionality
- 4+ card implementations with similar features
- Multiple Theme.of(context) calls in widgets
- Repetitive theme building code

### After Refactoring
- 2 unified button components covering all use cases
- 1 unified card component with configurable variants
- Optimized theme access patterns
- Consolidated theme building logic

## Performance Improvements

1. **Reduced Widget Tree Complexity**: Fewer widget types to instantiate
2. **Optimized Theme Access**: Batch theme data retrieval
3. **Code Splitting**: Better tree shaking with unified exports
4. **Memory Efficiency**: Fewer duplicate implementations in memory

## Backward Compatibility

Legacy components are still available but marked as deprecated. They will be removed in a future version. Use the migration guide above to update your code.

## Future Enhancements

- [ ] Add animation variants to unified components
- [ ] Implement responsive behavior in unified system
- [ ] Add accessibility improvements
- [ ] Create component documentation generator
- [ ] Add unit tests for all unified components
