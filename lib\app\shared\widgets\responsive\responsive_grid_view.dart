import 'package:flutter/material.dart';
import '../../../core/utils/responsive_utils.dart';

/// Responsive grid view
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double? spacing;
  final double? runSpacing;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final int? largeDesktopColumns;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ResponsiveGridView({
    super.key,
    required this.children,
    this.spacing,
    this.runSpacing,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.largeDesktopColumns,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveUtils.responsive(
      context,
      mobile: mobileColumns ?? 1,
      tablet: tabletColumns ?? 2,
      desktop: desktopColumns ?? 3,
      largeDesktop: largeDesktopColumns ?? 4,
    );

    final gridSpacing = spacing ?? ResponsiveUtils.responsiveSpacing(context);
    final gridRunSpacing = runSpacing ?? ResponsiveUtils.responsiveSpacing(context);
    final gridPadding = padding ?? ResponsiveUtils.responsivePadding(context);

    return GridView.count(
      crossAxisCount: columns,
      crossAxisSpacing: gridSpacing,
      mainAxisSpacing: gridRunSpacing,
      padding: gridPadding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      children: children,
    );
  }
}
