import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'cache_service.dart';
import 'performance_service.dart';

/// Service for managing app memory usage and optimization
class MemoryManagementService extends GetxService {
  static MemoryManagementService get to => Get.find();

  // Memory monitoring
  Timer? _memoryMonitorTimer;
  final RxDouble _currentMemoryUsage = 0.0.obs;
  final RxDouble _peakMemoryUsage = 0.0.obs;
  final RxBool _isLowMemoryMode = false.obs;

  // Memory thresholds (in MB)
  static const double lowMemoryThreshold = 150.0;
  static const double criticalMemoryThreshold = 200.0;
  static const double warningMemoryThreshold = 100.0;

  // Cleanup strategies
  final List<MemoryCleanupStrategy> _cleanupStrategies = [];

  // Getters
  double get currentMemoryUsage => _currentMemoryUsage.value;
  double get peakMemoryUsage => _peakMemoryUsage.value;
  bool get isLowMemoryMode => _isLowMemoryMode.value;

  @override
  void onInit() {
    super.onInit();
    _initializeMemoryManagement();
  }

  @override
  void onClose() {
    _memoryMonitorTimer?.cancel();
    super.onClose();
  }

  /// Initialize memory management
  void _initializeMemoryManagement() {
    _registerCleanupStrategies();
    _startMemoryMonitoring();
    _setupMemoryWarningListener();
  }

  /// Register cleanup strategies
  void _registerCleanupStrategies() {
    _cleanupStrategies.addAll([
      ImageCacheCleanupStrategy(),
      GetXControllerCleanupStrategy(),
      CustomCacheCleanupStrategy(),
      NetworkCacheCleanupStrategy(),
    ]);
  }

  /// Start memory monitoring
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkMemoryUsage();
    });
  }

  /// Setup memory warning listener
  void _setupMemoryWarningListener() {
    if (Platform.isIOS) {
      // iOS memory warning handling
      SystemChannels.lifecycle.setMessageHandler((message) async {
        if (message == AppLifecycleState.paused.toString()) {
          await _performMemoryCleanup(MemoryPressureLevel.moderate);
        }
        return null;
      });
    }
  }

  /// Check current memory usage
  Future<void> _checkMemoryUsage() async {
    try {
      final memoryUsage = await _getCurrentMemoryUsage();
      _currentMemoryUsage.value = memoryUsage;

      if (memoryUsage > _peakMemoryUsage.value) {
        _peakMemoryUsage.value = memoryUsage;
      }

      // Determine memory pressure level
      final pressureLevel = _getMemoryPressureLevel(memoryUsage);

      // Handle memory pressure
      await _handleMemoryPressure(pressureLevel);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking memory usage: $e');
      }
    }
  }

  /// Get current memory usage in MB
  Future<double> _getCurrentMemoryUsage() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        const platform = MethodChannel('memory/usage');
        final result = await platform.invokeMethod('getCurrentMemoryUsage');
        return (result as double?) ?? 0.0;
      }
      return 0.0;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting memory usage: $e');
      }
      return 0.0;
    }
  }

  /// Get memory pressure level
  MemoryPressureLevel _getMemoryPressureLevel(double memoryUsage) {
    if (memoryUsage >= criticalMemoryThreshold) {
      return MemoryPressureLevel.critical;
    } else if (memoryUsage >= lowMemoryThreshold) {
      return MemoryPressureLevel.high;
    } else if (memoryUsage >= warningMemoryThreshold) {
      return MemoryPressureLevel.moderate;
    } else {
      return MemoryPressureLevel.normal;
    }
  }

  /// Handle memory pressure
  Future<void> _handleMemoryPressure(MemoryPressureLevel level) async {
    switch (level) {
      case MemoryPressureLevel.critical:
        _isLowMemoryMode.value = true;
        await _performMemoryCleanup(level);
        _showMemoryWarning(
            'Critical memory usage detected. App performance may be affected.');
        break;

      case MemoryPressureLevel.high:
        _isLowMemoryMode.value = true;
        await _performMemoryCleanup(level);
        _showMemoryWarning('High memory usage detected. Optimizing...');
        break;

      case MemoryPressureLevel.moderate:
        await _performMemoryCleanup(level);
        break;

      case MemoryPressureLevel.normal:
        _isLowMemoryMode.value = false;
        break;
    }
  }

  /// Perform memory cleanup
  Future<void> _performMemoryCleanup(MemoryPressureLevel level) async {
    final timer = PerformanceService.to.startTimer('memory_cleanup');

    try {
      for (final strategy in _cleanupStrategies) {
        if (strategy.shouldExecute(level)) {
          await strategy.execute();
        }
      }

      // Force garbage collection
      await _forceGarbageCollection();

      if (kDebugMode) {
        print('🧹 Memory cleanup completed for level: $level');
      }
    } finally {
      timer.stop();
    }
  }

  /// Force garbage collection
  Future<void> _forceGarbageCollection() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        const platform = MethodChannel('memory/gc');
        await platform.invokeMethod('forceGarbageCollection');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error forcing garbage collection: $e');
      }
    }
  }

  /// Show memory warning
  void _showMemoryWarning(String message) {
    if (kDebugMode) {
      print('⚠️ Memory Warning: $message');
    }

    // In production, you might want to show a subtle notification
    // or log this to analytics
  }

  /// Manually trigger memory optimization
  Future<void> optimizeMemory() async {
    await _performMemoryCleanup(MemoryPressureLevel.high);
  }

  /// Get memory statistics
  Map<String, dynamic> getMemoryStatistics() {
    return {
      'current_usage_mb': _currentMemoryUsage.value,
      'peak_usage_mb': _peakMemoryUsage.value,
      'is_low_memory_mode': _isLowMemoryMode.value,
      'warning_threshold_mb': warningMemoryThreshold,
      'low_memory_threshold_mb': lowMemoryThreshold,
      'critical_threshold_mb': criticalMemoryThreshold,
    };
  }

  /// Register custom cleanup strategy
  void registerCleanupStrategy(MemoryCleanupStrategy strategy) {
    _cleanupStrategies.add(strategy);
  }
}

/// Memory pressure levels
enum MemoryPressureLevel {
  normal,
  moderate,
  high,
  critical,
}

/// Abstract cleanup strategy
abstract class MemoryCleanupStrategy {
  String get name;
  bool shouldExecute(MemoryPressureLevel level);
  Future<void> execute();
}

/// Image cache cleanup strategy
class ImageCacheCleanupStrategy extends MemoryCleanupStrategy {
  @override
  String get name => 'Image Cache Cleanup';

  @override
  bool shouldExecute(MemoryPressureLevel level) {
    return level == MemoryPressureLevel.moderate ||
        level == MemoryPressureLevel.high ||
        level == MemoryPressureLevel.critical;
  }

  @override
  Future<void> execute() async {
    PaintingBinding.instance.imageCache.clear();
    if (MemoryManagementService.to.isLowMemoryMode) {
      PaintingBinding.instance.imageCache.clearLiveImages();
    }
  }
}

/// GetX controller cleanup strategy
class GetXControllerCleanupStrategy extends MemoryCleanupStrategy {
  @override
  String get name => 'GetX Controller Cleanup';

  @override
  bool shouldExecute(MemoryPressureLevel level) {
    return level == MemoryPressureLevel.high ||
        level == MemoryPressureLevel.critical;
  }

  @override
  Future<void> execute() async {
    // Clean up unused GetX controllers
    Get.deleteAll(force: false);
  }
}

/// Custom cache cleanup strategy
class CustomCacheCleanupStrategy extends MemoryCleanupStrategy {
  @override
  String get name => 'Custom Cache Cleanup';

  @override
  bool shouldExecute(MemoryPressureLevel level) {
    return level == MemoryPressureLevel.high ||
        level == MemoryPressureLevel.critical;
  }

  @override
  Future<void> execute() async {
    // Clear custom caches
    await CacheService.to.clear();
  }
}

/// Network cache cleanup strategy
class NetworkCacheCleanupStrategy extends MemoryCleanupStrategy {
  @override
  String get name => 'Network Cache Cleanup';

  @override
  bool shouldExecute(MemoryPressureLevel level) {
    return level == MemoryPressureLevel.critical;
  }

  @override
  Future<void> execute() async {
    // Clear network caches if available
    // This would clear HTTP cache, etc.
  }
}
