import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/storage_service.dart';

class NotificationPreferencesController extends GetxController {
  final RxBool pushNotifications = true.obs;
  final RxBool emailNotifications = true.obs;
  final RxBool smsNotifications = false.obs;
  final RxBool marketingNotifications = false.obs;
  final RxBool securityNotifications = true.obs;
  final RxBool transactionNotifications = true.obs;
  final RxBool newsNotifications = false.obs;
  final RxBool promotionalNotifications = false.obs;

  // Notification timing
  final RxBool doNotDisturb = false.obs;
  final RxString doNotDisturbStart = '22:00'.obs;
  final RxString doNotDisturbEnd = '08:00'.obs;

  // Sound and vibration
  final RxBool notificationSound = true.obs;
  final RxBool vibration = true.obs;
  final RxString selectedTone = 'Default'.obs;

  final List<String> availableTones = [
    'Default',
    'Chime',
    'Bell',
    'Ding',
    'Pop',
    'Whistle',
  ];

  @override
  void onInit() {
    super.onInit();
    _loadPreferences();
  }

  void _loadPreferences() {
    final storage = StorageService.to;
    pushNotifications.value = storage.getBool('push_notifications');
    emailNotifications.value = storage.getBool('email_notifications');
    smsNotifications.value = storage.getBool('sms_notifications');
    marketingNotifications.value = storage.getBool('marketing_notifications');
    securityNotifications.value = storage.getBool('security_notifications');
    transactionNotifications.value =
        storage.getBool('transaction_notifications');
    newsNotifications.value = storage.getBool('news_notifications');
    promotionalNotifications.value =
        storage.getBool('promotional_notifications');

    doNotDisturb.value = storage.getBool('do_not_disturb');
    doNotDisturbStart.value = storage.getString('dnd_start').isEmpty
        ? '22:00'
        : storage.getString('dnd_start');
    doNotDisturbEnd.value = storage.getString('dnd_end').isEmpty
        ? '08:00'
        : storage.getString('dnd_end');

    notificationSound.value = storage.getBool('notification_sound');
    vibration.value = storage.getBool('vibration');
    selectedTone.value = storage.getString('selected_tone').isEmpty
        ? 'Default'
        : storage.getString('selected_tone');
  }

  void togglePushNotifications() {
    pushNotifications.value = !pushNotifications.value;
    StorageService.to.setBool('push_notifications', pushNotifications.value);
    _showFeedback('Push Notifications', pushNotifications.value);
  }

  void toggleEmailNotifications() {
    emailNotifications.value = !emailNotifications.value;
    StorageService.to.setBool('email_notifications', emailNotifications.value);
    _showFeedback('Email Notifications', emailNotifications.value);
  }

  void toggleSmsNotifications() {
    smsNotifications.value = !smsNotifications.value;
    StorageService.to.setBool('sms_notifications', smsNotifications.value);
    _showFeedback('SMS Notifications', smsNotifications.value);
  }

  void toggleMarketingNotifications() {
    marketingNotifications.value = !marketingNotifications.value;
    StorageService.to
        .setBool('marketing_notifications', marketingNotifications.value);
    _showFeedback('Marketing Notifications', marketingNotifications.value);
  }

  void toggleSecurityNotifications() {
    securityNotifications.value = !securityNotifications.value;
    StorageService.to
        .setBool('security_notifications', securityNotifications.value);
    _showFeedback('Security Notifications', securityNotifications.value);
  }

  void toggleTransactionNotifications() {
    transactionNotifications.value = !transactionNotifications.value;
    StorageService.to
        .setBool('transaction_notifications', transactionNotifications.value);
    _showFeedback('Transaction Notifications', transactionNotifications.value);
  }

  void toggleNewsNotifications() {
    newsNotifications.value = !newsNotifications.value;
    StorageService.to.setBool('news_notifications', newsNotifications.value);
    _showFeedback('News Notifications', newsNotifications.value);
  }

  void togglePromotionalNotifications() {
    promotionalNotifications.value = !promotionalNotifications.value;
    StorageService.to
        .setBool('promotional_notifications', promotionalNotifications.value);
    _showFeedback('Promotional Notifications', promotionalNotifications.value);
  }

  void toggleDoNotDisturb() {
    doNotDisturb.value = !doNotDisturb.value;
    StorageService.to.setBool('do_not_disturb', doNotDisturb.value);
    _showFeedback('Do Not Disturb', doNotDisturb.value);
  }

  void setDoNotDisturbStart(String time) {
    doNotDisturbStart.value = time;
    StorageService.to.setString('dnd_start', time);
  }

  void setDoNotDisturbEnd(String time) {
    doNotDisturbEnd.value = time;
    StorageService.to.setString('dnd_end', time);
  }

  void toggleNotificationSound() {
    notificationSound.value = !notificationSound.value;
    StorageService.to.setBool('notification_sound', notificationSound.value);
    _showFeedback('Notification Sound', notificationSound.value);
  }

  void toggleVibration() {
    vibration.value = !vibration.value;
    StorageService.to.setBool('vibration', vibration.value);
    _showFeedback('Vibration', vibration.value);
  }

  void changeNotificationTone(String tone) {
    selectedTone.value = tone;
    StorageService.to.setString('selected_tone', tone);
    Get.snackbar(
      'Notification Tone',
      'Changed to $tone',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void _showFeedback(String setting, bool enabled) {
    Get.snackbar(
      setting,
      enabled ? 'Enabled' : 'Disabled',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  Future<void> selectTime(String type) async {
    final TimeOfDay? picked = await Get.dialog<TimeOfDay>(
      TimePickerDialog(
        initialTime: TimeOfDay.now(),
      ),
    );

    if (picked != null) {
      final timeString =
          '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      if (type == 'start') {
        setDoNotDisturbStart(timeString);
      } else {
        setDoNotDisturbEnd(timeString);
      }
    }
  }
}
