import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Button style variants
enum ButtonVariant {
  filled,
  outlined,
  text,
  gradient,
}

/// Button size presets
enum ButtonSize {
  small,
  medium,
  large,
  custom,
}

/// Unified button widget that consolidates all button implementations
class UnifiedButton extends StatelessWidget {
  const UnifiedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = ButtonVariant.filled,
    this.size = ButtonSize.medium,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.icon,
    this.iconSize,
    this.iconSpacing = 8.0,
    this.textStyle,
    this.elevation = 0.0,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.gradient,
    this.responsive = false,
  });

  /// Filled button constructor
  const UnifiedButton.filled({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.icon,
    this.iconSize,
    this.iconSpacing = 8.0,
    this.textStyle,
    this.elevation = 2.0,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.responsive = false,
  })  : variant = ButtonVariant.filled,
        borderColor = null,
        borderWidth = 1.0,
        gradient = null;

  /// Outlined button constructor
  const UnifiedButton.outlined({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.icon,
    this.iconSize,
    this.iconSpacing = 8.0,
    this.textStyle,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.responsive = false,
  })  : variant = ButtonVariant.outlined,
        backgroundColor = null,
        elevation = 0.0,
        gradient = null;

  /// Text button constructor
  const UnifiedButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.icon,
    this.iconSize,
    this.iconSpacing = 8.0,
    this.textStyle,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.responsive = false,
  })  : variant = ButtonVariant.text,
        backgroundColor = null,
        borderColor = null,
        borderWidth = 1.0,
        elevation = 0.0,
        gradient = null;

  /// Gradient button constructor
  const UnifiedButton.gradient({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.gradient,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.icon,
    this.iconSize,
    this.iconSpacing = 8.0,
    this.textStyle,
    this.enabled = true,
    this.loading = false,
    this.loadingColor,
    this.responsive = false,
  })  : variant = ButtonVariant.gradient,
        backgroundColor = null,
        borderColor = null,
        borderWidth = 1.0,
        elevation = 0.0;

  final String text;
  final VoidCallback? onPressed;
  final ButtonVariant variant;
  final ButtonSize size;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final double borderWidth;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final IconData? icon;
  final double? iconSize;
  final double iconSpacing;
  final TextStyle? textStyle;
  final double elevation;
  final bool enabled;
  final bool loading;
  final Color? loadingColor;
  final List<Color>? gradient;
  final bool responsive;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = enabled && !loading && onPressed != null;

    // Get size-based dimensions
    final dimensions = _getSizeDimensions(context);
    final effectiveWidth = width ?? (responsive ? null : dimensions.width);
    final effectiveHeight = height ?? dimensions.height;
    final effectivePadding = padding ?? dimensions.padding;
    final effectiveBorderRadius =
        borderRadius ?? BorderRadius.circular(dimensions.borderRadius);

    // Get variant-based colors
    final colors = _getVariantColors(theme);

    Widget buttonChild = _buildButtonContent(theme, dimensions);

    if (variant == ButtonVariant.gradient) {
      return _buildGradientButton(
        theme,
        effectiveWidth,
        effectiveHeight,
        effectivePadding,
        effectiveBorderRadius,
        buttonChild,
        isEnabled,
      );
    }

    return Container(
      width: effectiveWidth,
      height: effectiveHeight,
      margin: margin,
      child: Material(
        color: isEnabled
            ? colors.backgroundColor
            : colors.backgroundColor.withValues(alpha: 0.6),
        elevation:
            variant == ButtonVariant.outlined || variant == ButtonVariant.text
                ? 0
                : elevation,
        borderRadius: effectiveBorderRadius,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: effectiveBorderRadius,
          child: Container(
            decoration:
                _buildDecoration(colors, effectiveBorderRadius, isEnabled),
            padding: effectivePadding,
            child: buttonChild,
          ),
        ),
      ),
    );
  }

  _ButtonDimensions _getSizeDimensions(BuildContext context) {
    switch (size) {
      case ButtonSize.small:
        return _ButtonDimensions(
          width: 120,
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          borderRadius: 8,
          textScale: 0.9,
        );
      case ButtonSize.medium:
        return _ButtonDimensions(
          width: 160,
          height: 48,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          borderRadius: 12,
          textScale: 1.0,
        );
      case ButtonSize.large:
        return _ButtonDimensions(
          width: 200,
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          borderRadius: 16,
          textScale: 1.1,
        );
      case ButtonSize.custom:
        return _ButtonDimensions(
          width: width ?? 160,
          height: height ?? 48,
          padding: padding ??
              const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          borderRadius: 12,
          textScale: 1.0,
        );
    }
  }

  _ButtonColors _getVariantColors(ThemeData theme) {
    switch (variant) {
      case ButtonVariant.filled:
        return _ButtonColors(
          backgroundColor: backgroundColor ?? theme.colorScheme.primary,
          foregroundColor: foregroundColor ?? theme.colorScheme.onPrimary,
          borderColor: borderColor ?? Colors.transparent,
        );
      case ButtonVariant.outlined:
        return _ButtonColors(
          backgroundColor: backgroundColor ?? Colors.transparent,
          foregroundColor: foregroundColor ?? theme.colorScheme.primary,
          borderColor: borderColor ?? theme.colorScheme.primary,
        );
      case ButtonVariant.text:
        return _ButtonColors(
          backgroundColor: backgroundColor ?? Colors.transparent,
          foregroundColor: foregroundColor ?? theme.colorScheme.primary,
          borderColor: borderColor ?? Colors.transparent,
        );
      case ButtonVariant.gradient:
        return _ButtonColors(
          backgroundColor: backgroundColor ?? Colors.transparent,
          foregroundColor: foregroundColor ?? Colors.white,
          borderColor: borderColor ?? Colors.transparent,
        );
    }
  }

  Widget _buildButtonContent(ThemeData theme, _ButtonDimensions dimensions) {
    final effectiveTextStyle = textStyle ??
        theme.textTheme.titleMedium?.copyWith(
          color: _getVariantColors(theme).foregroundColor,
          fontWeight: FontWeight.w600,
          fontSize: (theme.textTheme.titleMedium?.fontSize ?? 16) *
              dimensions.textScale,
        );

    if (loading) {
      return SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            loadingColor ?? _getVariantColors(theme).foregroundColor,
          ),
        ),
      );
    }

    final children = <Widget>[];

    if (icon != null) {
      children.add(Icon(
        icon,
        size: iconSize ?? (dimensions.height * 0.4),
        color: _getVariantColors(theme).foregroundColor,
      ));
      children.add(SizedBox(width: iconSpacing));
    }

    children.add(Flexible(
      child: Text(
        text,
        style: effectiveTextStyle,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    ));

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _buildGradientButton(
    ThemeData theme,
    double? effectiveWidth,
    double effectiveHeight,
    EdgeInsetsGeometry effectivePadding,
    BorderRadius effectiveBorderRadius,
    Widget buttonChild,
    bool isEnabled,
  ) {
    final auroraGradients = theme.auroraGradients;
    final effectiveGradient = gradient ??
        (auroraGradients?.primaryAuroraGradient ??
            [
              theme.colorScheme.primary,
              theme.colorScheme.primaryContainer,
            ]);

    return Container(
      width: effectiveWidth,
      height: effectiveHeight,
      margin: margin,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: effectiveGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: effectiveBorderRadius,
        boxShadow: [
          BoxShadow(
            color: effectiveGradient.first.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: effectiveBorderRadius,
          child: Container(
            padding: effectivePadding,
            child: buttonChild,
          ),
        ),
      ),
    );
  }

  BoxDecoration? _buildDecoration(
      _ButtonColors colors, BorderRadius borderRadius, bool isEnabled) {
    if (variant == ButtonVariant.outlined) {
      return BoxDecoration(
        border: Border.all(
          color: isEnabled
              ? colors.borderColor
              : colors.borderColor.withValues(alpha: 0.6),
          width: borderWidth,
        ),
        borderRadius: borderRadius,
      );
    }
    return null;
  }
}

class _ButtonDimensions {
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final double borderRadius;
  final double textScale;

  const _ButtonDimensions({
    required this.width,
    required this.height,
    required this.padding,
    required this.borderRadius,
    required this.textScale,
  });
}

class _ButtonColors {
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;

  const _ButtonColors({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.borderColor,
  });
}
