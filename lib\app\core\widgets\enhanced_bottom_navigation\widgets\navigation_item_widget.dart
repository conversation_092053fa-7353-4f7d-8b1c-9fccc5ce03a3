import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/navigation_item.dart';
import '../controllers/animation_controller_manager.dart';

/// Individual navigation item widget with animations
class NavigationItemWidget extends StatelessWidget {
  final EnhancedBottomNavigationItem item;
  final bool isSelected;
  final VoidCallback onTap;
  final Color selectedColor;
  final Color unselectedColor;
  final bool showLabel;
  final bool enableHapticFeedback;
  final bool enableRippleEffect;
  final AnimationControllerManager animationManager;

  const NavigationItemWidget({
    super.key,
    required this.item,
    required this.isSelected,
    required this.onTap,
    required this.selectedColor,
    required this.unselectedColor,
    required this.showLabel,
    required this.enableHapticFeedback,
    required this.enableRippleEffect,
    required this.animationManager,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (enableHapticFeedback) {
              HapticFeedback.lightImpact();
            }
            if (enableRippleEffect) {
              animationManager.triggerRipple();
            }
            onTap();
          },
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildIcon(),
                if (showLabel) ...[
                  const SizedBox(height: 4),
                  _buildLabel(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return AnimatedBuilder(
      animation: animationManager.iconBounceAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isSelected
              ? 1.0 + (animationManager.iconBounceAnimation.value * 0.1)
              : 1.0,
          child: SizedBox(
            width: 32,
            height: 32,
            child: Stack(
              alignment: Alignment.center,
              children: [
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    isSelected ? item.effectiveActiveIcon : item.icon,
                    key: ValueKey(isSelected),
                    color: isSelected ? selectedColor : unselectedColor,
                    size: 24,
                  ),
                ),
                if (item.badge != null)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: item.badge!,
                  ),
                if (item.badgeCount != null && item.badgeCount! > 0)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        item.badgeCount! > 99
                            ? '99+'
                            : item.badgeCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLabel() {
    return AnimatedOpacity(
      opacity: showLabel ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 200),
      child: AnimatedDefaultTextStyle(
        duration: const Duration(milliseconds: 200),
        style: TextStyle(
          color: isSelected ? selectedColor : unselectedColor,
          fontSize: 12,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          height: 1.0,
        ),
        child: Text(
          item.label,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
