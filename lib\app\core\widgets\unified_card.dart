import 'package:flutter/material.dart';

/// Card style variants
enum CardVariant {
  elevated,
  filled,
  outlined,
  flat,
}

/// Card size presets
enum CardSize {
  small,
  medium,
  large,
  custom,
}

/// Unified card widget that consolidates all card implementations
class UnifiedCard extends StatelessWidget {
  const UnifiedCard({
    super.key,
    required this.child,
    this.variant = CardVariant.elevated,
    this.size = CardSize.medium,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius,
    this.elevation,
    this.backgroundColor,
    this.surfaceTintColor,
    this.shadowColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.onTap,
    this.clipBehavior = Clip.antiAlias,
    this.customShadows,
  });

  /// Elevated card constructor (Material 3 style)
  const UnifiedCard.elevated({
    super.key,
    required this.child,
    this.size = CardSize.medium,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius,
    this.elevation,
    this.backgroundColor,
    this.surfaceTintColor,
    this.shadowColor,
    this.onTap,
    this.clipBehavior = Clip.antiAlias,
  }) : variant = CardVariant.elevated,
       borderColor = null,
       borderWidth = 1.0,
       customShadows = null;

  /// Filled card constructor
  const UnifiedCard.filled({
    super.key,
    required this.child,
    this.size = CardSize.medium,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.onTap,
    this.clipBehavior = Clip.antiAlias,
  }) : variant = CardVariant.filled,
       elevation = null,
       surfaceTintColor = null,
       shadowColor = null,
       borderColor = null,
       borderWidth = 1.0,
       customShadows = null;

  /// Outlined card constructor
  const UnifiedCard.outlined({
    super.key,
    required this.child,
    this.size = CardSize.medium,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.onTap,
    this.clipBehavior = Clip.antiAlias,
  }) : variant = CardVariant.outlined,
       elevation = null,
       surfaceTintColor = null,
       shadowColor = null,
       customShadows = null;

  /// Flat card constructor (no elevation, minimal styling)
  const UnifiedCard.flat({
    super.key,
    required this.child,
    this.size = CardSize.medium,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 0.0,
    this.onTap,
    this.clipBehavior = Clip.antiAlias,
    this.customShadows,
  }) : variant = CardVariant.flat,
       elevation = null,
       surfaceTintColor = null,
       shadowColor = null;

  final Widget child;
  final CardVariant variant;
  final CardSize size;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? borderRadius;
  final double? elevation;
  final Color? backgroundColor;
  final Color? surfaceTintColor;
  final Color? shadowColor;
  final Color? borderColor;
  final double borderWidth;
  final VoidCallback? onTap;
  final Clip clipBehavior;
  final List<BoxShadow>? customShadows;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Get size-based dimensions
    final dimensions = _getSizeDimensions();
    final effectiveWidth = width ?? dimensions.width;
    final effectiveHeight = height ?? dimensions.height;
    final effectivePadding = padding ?? dimensions.padding;
    final effectiveBorderRadius = borderRadius ?? dimensions.borderRadius;
    
    // Get variant-based styling
    final styling = _getVariantStyling(theme);
    
    Widget card = Container(
      width: effectiveWidth,
      height: effectiveHeight,
      margin: margin,
      child: _buildCardContent(
        theme,
        effectivePadding,
        effectiveBorderRadius,
        styling,
      ),
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        child: card,
      );
    }

    return card;
  }

  _CardDimensions _getSizeDimensions() {
    switch (size) {
      case CardSize.small:
        return const _CardDimensions(
          width: null,
          height: null,
          padding: EdgeInsets.all(12),
          borderRadius: 8,
        );
      case CardSize.medium:
        return const _CardDimensions(
          width: null,
          height: null,
          padding: EdgeInsets.all(16),
          borderRadius: 12,
        );
      case CardSize.large:
        return const _CardDimensions(
          width: null,
          height: null,
          padding: EdgeInsets.all(20),
          borderRadius: 16,
        );
      case CardSize.custom:
        return _CardDimensions(
          width: width,
          height: height,
          padding: padding ?? const EdgeInsets.all(16),
          borderRadius: borderRadius ?? 12,
        );
    }
  }

  _CardStyling _getVariantStyling(ThemeData theme) {
    switch (variant) {
      case CardVariant.elevated:
        return _CardStyling(
          backgroundColor: backgroundColor ?? theme.colorScheme.surface,
          elevation: elevation ?? 2.0,
          surfaceTintColor: surfaceTintColor ?? theme.colorScheme.surfaceTint,
          shadowColor: shadowColor ?? theme.colorScheme.shadow,
          borderColor: null,
          borderWidth: 0.0,
          boxShadows: null,
        );
      case CardVariant.filled:
        return _CardStyling(
          backgroundColor: backgroundColor ?? theme.colorScheme.surfaceContainerHighest,
          elevation: 0.0,
          surfaceTintColor: null,
          shadowColor: null,
          borderColor: null,
          borderWidth: 0.0,
          boxShadows: null,
        );
      case CardVariant.outlined:
        return _CardStyling(
          backgroundColor: backgroundColor ?? theme.colorScheme.surface,
          elevation: 0.0,
          surfaceTintColor: null,
          shadowColor: null,
          borderColor: borderColor ?? theme.colorScheme.outline,
          borderWidth: borderWidth,
          boxShadows: null,
        );
      case CardVariant.flat:
        return _CardStyling(
          backgroundColor: backgroundColor ?? theme.colorScheme.surface,
          elevation: 0.0,
          surfaceTintColor: null,
          shadowColor: null,
          borderColor: borderColor,
          borderWidth: borderWidth,
          boxShadows: customShadows,
        );
    }
  }

  Widget _buildCardContent(
    ThemeData theme,
    EdgeInsetsGeometry effectivePadding,
    double effectiveBorderRadius,
    _CardStyling styling,
  ) {
    if (variant == CardVariant.elevated) {
      return Card(
        elevation: styling.elevation,
        color: styling.backgroundColor,
        surfaceTintColor: styling.surfaceTintColor,
        shadowColor: styling.shadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius),
        ),
        clipBehavior: clipBehavior,
        child: Padding(
          padding: effectivePadding,
          child: child,
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: styling.backgroundColor,
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        border: styling.borderColor != null
            ? Border.all(
                color: styling.borderColor!,
                width: styling.borderWidth,
              )
            : null,
        boxShadow: styling.boxShadows,
      ),
      clipBehavior: clipBehavior,
      child: Padding(
        padding: effectivePadding,
        child: child,
      ),
    );
  }
}

class _CardDimensions {
  final double? width;
  final double? height;
  final EdgeInsetsGeometry padding;
  final double borderRadius;

  const _CardDimensions({
    required this.width,
    required this.height,
    required this.padding,
    required this.borderRadius,
  });
}

class _CardStyling {
  final Color backgroundColor;
  final double elevation;
  final Color? surfaceTintColor;
  final Color? shadowColor;
  final Color? borderColor;
  final double borderWidth;
  final List<BoxShadow>? boxShadows;

  const _CardStyling({
    required this.backgroundColor,
    required this.elevation,
    required this.surfaceTintColor,
    required this.shadowColor,
    required this.borderColor,
    required this.borderWidth,
    required this.boxShadows,
  });
}
