import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';
import 'flat_card.dart';

/// Feature card component for displaying app features with icon, title, and description
class FeatureCard extends StatelessWidget {
  const FeatureCard({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.gradient,
    this.iconSize = 24.0,
    this.iconContainerSize = 48.0,
    this.onTap,
    this.padding = const EdgeInsets.all(16),
  });

  final IconData icon;
  final String title;
  final String description;
  final List<Color>? gradient;
  final double iconSize;
  final double iconContainerSize;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry padding;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final auroraGradients = theme.auroraGradients;
    final effectiveGradient = gradient ?? auroraGradients.primaryAuroraGradient;

    return FlatCard(
      padding: padding,
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon container with improved gradient and shadow
          Container(
            width: iconContainerSize,
            height: iconContainerSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: effectiveGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: effectiveGradient.first.withValues(alpha: 0.4),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: effectiveGradient.last.withValues(alpha: 0.2),
                  blurRadius: 24,
                  offset: const Offset(0, 12),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Icon(
              icon,
              size: iconSize,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 16),

          // Title with improved typography
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
              letterSpacing: 0.1,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 8),

          // Description with improved readability
          Text(
            description,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.75),
              height: 1.4,
              letterSpacing: 0.05,
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
