import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'cache_service.dart';
import 'performance_service.dart';

/// Service for optimizing images and managing image cache
class ImageOptimizationService extends GetxService {
  static ImageOptimizationService get to => Get.find();

  // Image cache configuration
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int maxCacheItems = 1000;
  static const Duration cacheExpiration = Duration(days: 7);

  // Image optimization settings
  static const int maxImageWidth = 1920;
  static const int maxImageHeight = 1080;
  static const int thumbnailSize = 200;
  static const int quality = 85;

  @override
  void onInit() {
    super.onInit();
    _configureImageCache();
  }

  /// Configure Flutter's image cache
  void _configureImageCache() {
    PaintingBinding.instance.imageCache.maximumSize = maxCacheItems;
    PaintingBinding.instance.imageCache.maximumSizeBytes = maxCacheSize;
  }

  /// Optimize image file
  Future<File?> optimizeImage(
    File imageFile, {
    int? maxWidth,
    int? maxHeight,
    int? quality,
    bool generateThumbnail = false,
  }) async {
    final timer = PerformanceService.to.startTimer('image_optimization');

    try {
      final bytes = await imageFile.readAsBytes();
      final optimizedBytes = await _optimizeImageBytes(
        bytes,
        maxWidth: maxWidth ?? maxImageWidth,
        maxHeight: maxHeight ?? maxImageHeight,
        quality: quality ?? ImageOptimizationService.quality,
      );

      if (optimizedBytes != null) {
        final optimizedFile = File('${imageFile.path}_optimized.jpg');
        await optimizedFile.writeAsBytes(optimizedBytes);

        if (generateThumbnail) {
          await _generateThumbnail(optimizedBytes, imageFile.path);
        }

        return optimizedFile;
      }

      return null;
    } catch (e) {
      debugPrint('Error optimizing image: $e');
      return null;
    } finally {
      timer.stop();
    }
  }

  /// Optimize image bytes
  Future<Uint8List?> _optimizeImageBytes(
    Uint8List bytes, {
    required int maxWidth,
    required int maxHeight,
    required int quality,
  }) async {
    try {
      final codec = await ui.instantiateImageCodec(
        bytes,
        targetWidth: maxWidth,
        targetHeight: maxHeight,
      );

      final frame = await codec.getNextFrame();
      final image = frame.image;

      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        return byteData.buffer.asUint8List();
      }

      return null;
    } catch (e) {
      debugPrint('Error optimizing image bytes: $e');
      return null;
    }
  }

  /// Generate thumbnail
  Future<File?> _generateThumbnail(
      Uint8List imageBytes, String originalPath) async {
    try {
      final thumbnailBytes = await _optimizeImageBytes(
        imageBytes,
        maxWidth: thumbnailSize,
        maxHeight: thumbnailSize,
        quality: 70,
      );

      if (thumbnailBytes != null) {
        final thumbnailFile = File('${originalPath}_thumbnail.jpg');
        await thumbnailFile.writeAsBytes(thumbnailBytes);
        return thumbnailFile;
      }

      return null;
    } catch (e) {
      debugPrint('Error generating thumbnail: $e');
      return null;
    }
  }

  /// Cached network image widget
  Widget cachedNetworkImage(
    String url, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
    bool enableMemoryCache = true,
  }) {
    return FutureBuilder<String?>(
      future: _getCachedImagePath(url),
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data != null) {
          // Show cached image
          return Image.file(
            File(snapshot.data!),
            width: width,
            height: height,
            fit: fit,
            errorBuilder: (context, error, stackTrace) {
              return errorWidget ?? _defaultErrorWidget();
            },
          );
        } else if (snapshot.connectionState == ConnectionState.waiting) {
          // Show placeholder while loading
          return placeholder ?? _defaultPlaceholder(width, height);
        } else {
          // Load and cache image
          return _NetworkImageLoader(
            url: url,
            width: width,
            height: height,
            fit: fit,
            placeholder: placeholder,
            errorWidget: errorWidget,
            enableMemoryCache: enableMemoryCache,
          );
        }
      },
    );
  }

  /// Get cached image path
  Future<String?> _getCachedImagePath(String url) async {
    return await CacheService.to.getCachedImagePath(url);
  }

  /// Preload images for better performance
  Future<void> preloadImages(List<String> imageUrls) async {
    final timer = PerformanceService.to.startTimer('preload_images');

    try {
      final futures = imageUrls.map((url) => _preloadSingleImage(url));
      await Future.wait(futures);
    } finally {
      timer.stop();
    }
  }

  /// Preload single image
  Future<void> _preloadSingleImage(String url) async {
    try {
      final cachedPath = await _getCachedImagePath(url);
      if (cachedPath == null) {
        // Download and cache image
        await _downloadAndCacheImage(url);
      }
    } catch (e) {
      debugPrint('Error preloading image $url: $e');
    }
  }

  /// Download and cache image
  Future<void> _downloadAndCacheImage(String url) async {
    try {
      // This would typically use a network service to download the image
      // For now, we'll simulate the process

      // In a real implementation, you would:
      // 1. Download the image bytes from the URL
      // 2. Optimize the image if needed
      // 3. Cache the image using CacheService

      debugPrint('Downloading and caching image: $url');
    } catch (e) {
      debugPrint('Error downloading image $url: $e');
    }
  }

  /// Clear image cache
  Future<void> clearImageCache() async {
    final timer = PerformanceService.to.startTimer('clear_image_cache');

    try {
      // Clear Flutter's image cache
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Clear custom cache
      // This would clear cached images from CacheService

      debugPrint('Image cache cleared');
    } finally {
      timer.stop();
    }
  }

  /// Get image cache size
  Future<String> getImageCacheSize() async {
    // This would calculate the total size of cached images
    // For now, return a placeholder
    return '0 MB';
  }

  /// Default placeholder widget
  Widget _defaultPlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  /// Default error widget
  Widget _defaultErrorWidget() {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: Icon(
          Icons.error_outline,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }
}

/// Network image loader widget
class _NetworkImageLoader extends StatefulWidget {
  final String url;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;

  const _NetworkImageLoader({
    required this.url,
    this.width,
    this.height,
    required this.fit,
    this.placeholder,
    this.errorWidget,
    required this.enableMemoryCache,
  });

  @override
  State<_NetworkImageLoader> createState() => _NetworkImageLoaderState();
}

class _NetworkImageLoaderState extends State<_NetworkImageLoader> {
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Simulate image loading and caching
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real implementation, this would:
      // 1. Download the image from the URL
      // 2. Optimize and cache it
      // 3. Return the cached path

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.placeholder ??
          ImageOptimizationService.to
              ._defaultPlaceholder(widget.width, widget.height);
    }

    if (_hasError) {
      return widget.errorWidget ??
          ImageOptimizationService.to._defaultErrorWidget();
    }

    // In a real implementation, this would show the cached image
    return Image.network(
      widget.url,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return widget.placeholder ??
            ImageOptimizationService.to
                ._defaultPlaceholder(widget.width, widget.height);
      },
      errorBuilder: (context, error, stackTrace) {
        return widget.errorWidget ??
            ImageOptimizationService.to._defaultErrorWidget();
      },
    );
  }
}
