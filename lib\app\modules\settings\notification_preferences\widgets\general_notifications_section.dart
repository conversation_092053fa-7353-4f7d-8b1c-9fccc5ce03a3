import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../shared/widgets/section_header.dart';
import '../../../../shared/widgets/privacy_setting_item.dart';
import '../notification_preferences_controller.dart';

/// General notification settings section
class GeneralNotificationsSection extends GetView<NotificationPreferencesController> {
  const GeneralNotificationsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'General'),
        const SizedBox(height: 12),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.notifications,
          iconColor: Colors.blue,
          title: 'Push Notifications',
          subtitle: 'Receive push notifications',
          value: controller.pushNotifications.value,
          onChanged: controller.togglePushNotifications,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.email,
          iconColor: Colors.green,
          title: 'Email Notifications',
          subtitle: 'Receive notifications via email',
          value: controller.emailNotifications.value,
          onChanged: controller.toggleEmailNotifications,
          activeColor: theme.colorScheme.primary,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => PrivacySettingItem(
          icon: Icons.sms,
          iconColor: Colors.orange,
          title: 'SMS Notifications',
          subtitle: 'Receive notifications via SMS',
          value: controller.smsNotifications.value,
          onChanged: controller.toggleSmsNotifications,
          activeColor: theme.colorScheme.primary,
        )),
      ],
    );
  }
}
