import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../constants/app_constants.dart';

class StorageService extends GetxService {
  static StorageService get to => Get.find();

  late GetStorage _box;
  bool _isInitialized = false;

  StorageService() {
    // Initialize with a temporary box that will be replaced
    _box = GetStorage();
    _initializeStorage();
  }

  Future<void> _initializeStorage() async {
    try {
      await GetStorage.init();
      _box = GetStorage();
      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing storage: $e');
      // Keep the existing box as fallback
      _isInitialized = true;
    }
  }

  Future<StorageService> init() async {
    if (!_isInitialized) {
      await _initializeStorage();
    }
    return this;
  }

  // Generic methods
  T? read<T>(String key) {
    try {
      return _box.read<T>(key);
    } catch (e) {
      debugPrint('Error reading from storage: $e');
      return null;
    }
  }

  Future<void> write(String key, dynamic value) async {
    try {
      await _box.write(key, value);
    } catch (e) {
      debugPrint('Error writing to storage: $e');
    }
  }

  Future<void> remove(String key) async {
    try {
      await _box.remove(key);
    } catch (e) {
      debugPrint('Error removing from storage: $e');
    }
  }

  Future<void> clear() async {
    try {
      await _box.erase();
    } catch (e) {
      debugPrint('Error clearing storage: $e');
    }
  }

  // App specific methods
  bool get isFirstTime => read<bool>(AppConstants.keyIsFirstTime) ?? true;
  set isFirstTime(bool value) => write(AppConstants.keyIsFirstTime, value);

  String? get accessToken => read<String>(AppConstants.keyAccessToken);
  set accessToken(String? value) {
    if (value != null) {
      write(AppConstants.keyAccessToken, value);
    } else {
      remove(AppConstants.keyAccessToken);
    }
  }

  String? get refreshToken => read<String>(AppConstants.keyRefreshToken);
  set refreshToken(String? value) {
    if (value != null) {
      write(AppConstants.keyRefreshToken, value);
    } else {
      remove(AppConstants.keyRefreshToken);
    }
  }

  Map<String, dynamic>? get userData =>
      read<Map<String, dynamic>>(AppConstants.keyUserData);
  set userData(Map<String, dynamic>? value) {
    if (value != null) {
      write(AppConstants.keyUserData, value);
    } else {
      remove(AppConstants.keyUserData);
    }
  }

  String? get themeMode => read<String>(AppConstants.keyThemeMode);
  set themeMode(String? value) {
    if (value != null) {
      write(AppConstants.keyThemeMode, value);
    } else {
      remove(AppConstants.keyThemeMode);
    }
  }

  bool get isLoggedIn => accessToken != null;

  bool get isDarkMode => read<bool>('dark_mode') ?? false;

  // Helper methods for settings
  bool getBool(String key) => read<bool>(key) ?? false;
  String getString(String key) => read<String>(key) ?? '';
  double getDouble(String key) => read<double>(key) ?? 0.0;
  int getInt(String key) => read<int>(key) ?? 0;

  Future<void> setBool(String key, bool value) async {
    await write(key, value);
  }

  Future<void> setString(String key, String value) async {
    await write(key, value);
  }

  Future<void> setDouble(String key, double value) async {
    await write(key, value);
  }

  Future<void> setInt(String key, int value) async {
    await write(key, value);
  }

  Future<void> clearUserData() async {
    await remove(AppConstants.keyAccessToken);
    await remove(AppConstants.keyRefreshToken);
    await remove(AppConstants.keyUserData);
  }
}
