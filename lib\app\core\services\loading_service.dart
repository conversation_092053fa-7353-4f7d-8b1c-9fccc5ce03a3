import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Centralized loading state management service
class LoadingService extends GetxService {
  static LoadingService get to => Get.find();

  // Global loading states
  final RxMap<String, bool> _loadingStates = <String, bool>{}.obs;
  final RxBool _globalLoading = false.obs;

  // Getters
  bool get isGlobalLoading => _globalLoading.value;
  Map<String, bool> get loadingStates => _loadingStates;

  /// Show global loading overlay
  void showGlobalLoading({
    String? message,
    bool barrierDismissible = false,
  }) {
    if (_globalLoading.value) return;

    _globalLoading.value = true;

    Get.dialog(
      LoadingDialog(message: message),
      barrierDismissible: barrierDismissible,
      name: 'global_loading',
    );
  }

  /// Hide global loading overlay
  void hideGlobalLoading() {
    if (!_globalLoading.value) return;

    _globalLoading.value = false;

    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }

  /// Set loading state for a specific key
  void setLoading(String key, bool isLoading) {
    _loadingStates[key] = isLoading;
  }

  /// Get loading state for a specific key
  bool isLoading(String key) {
    return _loadingStates[key] ?? false;
  }

  /// Clear all loading states
  void clearAllLoading() {
    _loadingStates.clear();
    hideGlobalLoading();
  }

  /// Execute a function with loading state management
  Future<T> withLoading<T>(
    String key,
    Future<T> Function() function, {
    bool showGlobal = false,
    String? globalMessage,
  }) async {
    try {
      setLoading(key, true);
      if (showGlobal) {
        showGlobalLoading(message: globalMessage);
      }

      return await function();
    } finally {
      setLoading(key, false);
      if (showGlobal) {
        hideGlobalLoading();
      }
    }
  }

  /// Execute a function with global loading
  Future<T> withGlobalLoading<T>(
    Future<T> Function() function, {
    String? message,
    bool barrierDismissible = false,
  }) async {
    try {
      showGlobalLoading(
        message: message,
        barrierDismissible: barrierDismissible,
      );
      return await function();
    } finally {
      hideGlobalLoading();
    }
  }

  /// Show loading snackbar
  void showLoadingSnackbar(String message) {
    Get.snackbar(
      'Loading',
      message,
      snackPosition: SnackPosition.BOTTOM,
      showProgressIndicator: true,
      duration: const Duration(seconds: 30), // Long duration for loading
      backgroundColor: Colors.blue.withValues(alpha: 0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.hourglass_empty, color: Colors.white),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
    );
  }

  /// Hide loading snackbar
  void hideLoadingSnackbar() {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
  }
}

/// Custom loading dialog widget
class LoadingDialog extends StatelessWidget {
  final String? message;

  const LoadingDialog({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return PopScope(
      canPop: false,
      child: Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Loading indicator
              SizedBox(
                width: 48,
                height: 48,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Loading message
              Text(
                message ?? 'Loading...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Loading state mixin for controllers
mixin LoadingStateMixin on GetxController {
  final RxBool isLoading = false.obs;
  final RxString loadingMessage = ''.obs;

  /// Set loading state with optional message
  void setLoadingState(bool loading, [String? message]) {
    isLoading.value = loading;
    if (message != null) {
      loadingMessage.value = message;
    }
  }

  /// Execute function with loading state
  Future<T> executeWithLoading<T>(
    Future<T> Function() function, {
    String? message,
    bool showGlobal = false,
  }) async {
    try {
      setLoadingState(true, message);
      if (showGlobal) {
        LoadingService.to.showGlobalLoading(message: message);
      }
      return await function();
    } finally {
      setLoadingState(false);
      if (showGlobal) {
        LoadingService.to.hideGlobalLoading();
      }
    }
  }
}

/// Loading widget for UI components
class LoadingWidget extends StatelessWidget {
  final String? message;
  final double? size;
  final Color? color;
  final bool showMessage;

  const LoadingWidget({
    super.key,
    this.message,
    this.size,
    this.color,
    this.showMessage = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size ?? 32,
            height: size ?? 32,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? theme.colorScheme.primary,
              ),
            ),
          ),
          if (showMessage && message != null) ...[
            const SizedBox(height: 12),
            Text(
              message!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Shimmer loading effect widget
class ShimmerLoading extends StatefulWidget {
  final Widget child;
  final bool isLoading;
  final Color? baseColor;
  final Color? highlightColor;

  const ShimmerLoading({
    super.key,
    required this.child,
    required this.isLoading,
    this.baseColor,
    this.highlightColor,
  });

  @override
  State<ShimmerLoading> createState() => _ShimmerLoadingState();
}

class _ShimmerLoadingState extends State<ShimmerLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    if (widget.isLoading) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(ShimmerLoading oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading) {
      return widget.child;
    }

    final theme = Theme.of(context);
    final baseColor =
        widget.baseColor ?? theme.colorScheme.surfaceContainerHighest;
    final highlightColor = widget.highlightColor ?? theme.colorScheme.surface;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [baseColor, highlightColor, baseColor],
              stops: [
                (_animation.value - 1).clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0),
                (_animation.value + 1).clamp(0.0, 1.0),
              ],
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}
